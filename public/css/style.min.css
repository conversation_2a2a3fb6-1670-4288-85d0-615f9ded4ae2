@charset "UTF-8";

a, abbr, acronym, address, applet, article, aside, audio, b, big, blockquote, body, canvas, caption, center, cite, code, dd, del, details, dfn, div, dl, dt, em, embed, fieldset, figcaption, figure, footer, form, h1, h2, h3, h4, h5, h6, header, hgroup, html, i, iframe, img, ins, kbd, label, legend, li, mark, menu, nav, object, ol, output, p, pre, q, ruby, s, samp, section, small, span, strike, strong, sub, summary, sup, table, tbody, td, tfoot, th, thead, time, tr, tt, u, ul, var, video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline
}

article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
    display: block
}

body {
    line-height: 1;
    transition: 0.4s all ease-in;

}

:root {
    --pbmit-global-color:  #d02333;
    --pbmit-secondary-color: #222d35;
    --pbmit-light-color: #f9f9f9;
    --pbmit-white-color:#ffffff;
    --pbmit-blackish-color: #2f4858;
    --pbmit-link-color-normal: #222d35;
    --pbmit-link-color-hover:#c3002f;
    --pbmit-global-color-rgb:rgb(195, 0, 47);
    --pbmit-secondary-color-rgb: rgb(34, 45, 53);
    --pbmit-responsive-breakpoint: 1200px;

}


ol, ul {
    list-style: none
}

blockquote, q {
    quotes: none
}

blockquote:after, blockquote:before {
    content: "";
    content: none
}

q:after, q:before {
    content: "";
    content: none
}

table {
    border-collapse: collapse;
    border-spacing: 0
}

@font-face {
    font-family: p_bold;
    src: url(../fonts/Inter-Bold.ttf) format("truetype");
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: p_regular;
    src: url(../fonts/Inter-Regular.ttf) format("truetype");
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: p_medium;
    src: url(../fonts/Inter-Medium.ttf) format("truetype");
    font-weight: 400;
    font-style: normal
}

.about-header-two__title h1, .about-header-two__title span, .about-header__title h1, .about-header__title span, .about-item figure span, .about-item h2, .authors-item__title h3, .blog-items__item .title-in h2, .choose-c__item h2, .choose-c__item ul li.active a, .contact-title h1, .contact-title span, .content-list__desc ul li, .courses-full__admin .tx h3, .courses-full__admin .us-item__title h3, .courses-full__tabs .tab-content .tab-pane .content-in h3, .courses-full__tabs .tab-content .tab-pane .content-in strong, .courses-full__tabs .tab-content .tab-pane .content-in ul li:before, .courses-full__title h1, .courses-slider__item a .title-in h2, .cover .spead h1, .dashboard-content__title h3, .dashboard-header__balance .ptitle h2, .dashboard-header__balance .ptitle p, .dashboard-header__name .ptitle a, .dashboard-header__name .ptitle h2, .dashboard-header__wishlist .pimage span, .dashboard-header__wishlist .ptitle h2, .dashboard-header__wishlist .ptitle p, .down-history h3, .down-list ul li span, .edit-profile__avatar-info, .edit-profile__avatar-info a, .edit-profile__avatar-info figcaption, .edit-profile__content h1, .filter-block__title h1, .filter-block__titletop h1, .footer .footer-block h3, .header-menu__actions a span, .hero .counter, .hero-slider__title h1, .import-title h2, .import-title span, .industry-top__title h1, .industry-top__title span, .l-item__img .price, .l-item__title h2, .l-item__title ul li span, .login-form h1, .loginform h3, .map-title h2, .map-title span, .news-full__desc .title-out h1, .pricing-table .duration, .pricing-table .price, .section-title h1, .section-title span, .services-item__title h2, .speadbar-item h1, .speadbar-title h1, .stats-items__item .number, .stats-title h2, .stats-title span, .testimonials-item__desc h2, .testimonials-item__img .title-it span, .userloginform-buttons__social ul li, .video h1, .why_us-item .title-in h3 {
    font-family: 'p_bold', sans-serif;
    font-weight: 700
}

.header-menu__nav ul li a, .loginform, body {
    font-family: 'p_regular', sans-serif;
    font-weight: 400
}

.blog-items__item .title-in a, .section-title h2 {
    font-family: 'p_medium', sans-serif;
    font-weight: 500
}

.about-header__img figure img, .about-text__img figure img, .authors-item__img figure img, .blog-items__item figure img, .cart-item__main .name figure img, .choose-c__item ul li figure img, .courses-full__admin .us-item__img figure img, .courses-full__infos ul li a figure img, .dashboard-header__name .pimage figure img, .gallery-block__item figure img, .hero-slider__img figure img, .l-item__img figure img, .map-item figure img, .news-full__img figure img, .news-item figure img, .profile-sidebar__img .p-img2__item figure img, .testimonials-img figure img, .testimonials-item__img figure img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover
}

.about-header-two__img figure, .about-header-two__title a, .about-header__title a, .cart-item .qty input, .cart-item .qty span, .choose-item .date a, .courses-filters .cats li a figure, .courses-full__tabs .tab-content .tab-pane .content-in ul li:before, .courses-slider__item a figure, .dashboard-content__title, .dashboard-header__balance .pimage figure, .dashboard-header__balance .pimage figure a, .dashboard-header__wishlist .pimage figure, .dashboard-header__wishlist .pimage figure a, .gallery-block__item a.view, .header-menu__actions a span, .hero-slider__title a, .i-brands__item, .import-logos__item, .import-logos__item a, .import-logos__item a figure, .import-title a, .l-item__title ul li figure, .partners-item__carousel-item figure, .services-item figure, .stats-title a, .video h1 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.about-header, .about-header-two, .about-header-two__img figure, .about-header__img figure, .about-text figcaption, .about-text figcaption p, .about-text__img, .about-text__img figure, .all, .all h1, .authors-item, .authors-item__img figure, .blog-items, .blog-items__item, .blog-items__item .title-in h2, .blog-items__item .title-in p, .blog-items__item .title-in span, .cart .section-title h2, .cart-item, .cart-item__main, .cart-item__top, .ch, .choose, .choose-c, .choose-item, .choose-item .date, .claim-text, .contact, .contact form .form-group, .contact form .form-group input, .contact form .form-group textarea, .courses-filters .cats, .courses-full, .courses-full__admin, .courses-full__admin .icons ul, .courses-full__admin .tx, .courses-full__admin .tx p, .courses-full__admin .us-item, .courses-full__admin .us-item__img figure, .courses-full__infos, .courses-full__infos ul li ul li, .courses-full__tabs, .courses-full__tabs .tab-content, .courses-full__tabs .tab-content .tab-pane, .courses-full__tabs .tab-content .tab-pane .btns-in, .courses-full__tabs .tab-content .tab-pane .content-in, .courses-full__tabs .tab-content .tab-pane .content-in h3, .courses-full__tabs .tab-content .tab-pane .content-in p, .courses-full__tabs .tab-content .tab-pane .content-in ul li, .courses-full__title h1, .courses-slider, .courses-slider__item, .courses-slider__item a, .cover, .dashboard, .dashboard-content, .down, .faq-list__item, .filter-block__titletop, .footer, .footer .footer-block .socials, .footer .footer-block ul li, .footer .footer-logo, .gallery-block__item, .gallery-block__item figure, .header, .header-menu, .hero, .hero-slider, .hero-slider__img figure, .i-brands__item figure, .i-brands__list, .in-brands, .in-brands__list, .in-brands__list-item, .industry, .industry-top, .l-item, .l-item__img figure, .l-item__title ul, .l-item__title ul li, .login-form h1, .map-item figure, .news-full, .news-full__desc, .news-full__desc .title-out, .blog-content, .blog-content p, .news-full__img, .news-full__img figure, .news-item, .news-item figure, .partners-item__carousel-item, .partners-item__carousel-item figure, .pricing-section, .pricing-section .nav-tabs, .profile, .profile .change-password, .profile-edit, .profile-edit .form-group, .profile-edit .form-group input, .profile-edit .form-group label, .profile-sidebar, .profile-sidebar__img, .profile-sidebar__menu, .profile-sidebar__menu ul li, .promo, .promo .have, .section, .section-title, .section-title h1, .section-title ul, .services, .services-item, .spead, .testimonials-img figure, .testimonials-item, .testimonials-item__desc, .testimonials-item__desc h2, .testimonials-item__desc p, .testimonials-item__img, .testimonials-item__img .title-it span, .video, .why_us-item, .why_us-item .title-in {
    width: 100%;
    float: left
}

.about-header-two__title a, .about-header__title a, .btn-reg, .ch, .contact form button, .courses-full__tabs .tab-content .tab-pane .btns-in a, .hero-slider__title a, .import-title a, .load-more, .section-title ul li a, .stats-title a {
    padding: 15px 30px;
    font-size:   14px;
    border-radius: 10px
}

h1 {
    font-size: 21px
}

h2 {
    font-size: 18px
}

h3 {
    font-size: 16px
}

h4 {
    font-size: 14px
}

button, input, select, textarea {
    border: none;
    outline: 0;
    background: 0 0
}

button:focus, select:focus, span:focus {
    outline: 0;
    text-decoration: none
}

a:hover {
    text-decoration: none
}

body {
    font-size:   14px;
    background: #fbfbfb;
    overflow-x: hidden
}

body::-webkit-scrollbar {
    width: 5px
}

body::-webkit-scrollbar-track {
    background: #fff
}

body::-webkit-scrollbar-thumb {
    background: #d02333
}

.container {
    max-width: 1280px;
}

.header {
    position: relative;
    height: 103px;
    z-index: 300
}

.header-menu {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.header-menu__logo {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.header-menu__logo svg {
    height: 100px;
    margin-left: -55px;
    width: 200px
}

.header-menu__logo svg path {
    fill: #d02333
}

.header-menu__nav {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-right: -190px
}

.header-menu__nav ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.header-menu__nav ul li {
    float: left;
    margin-left: 30px;
    position:relative;
}



.header-menu__nav ul li:first-child {
    margin-left: 0
}

.header-menu__nav ul li a {
    color: #fff;
    font-size: 14px;
    color: #28272c;

}

.header-menu__nav ul li a:hover {
    color: #d02333
}

.header-menu__nav ul li.active a {
    color: #d02333
}

.header-menu__nav ul li ul {
    position: absolute;
    top:30px;
    border:none;
    box-shadow: 10px 15px 30px rgba(1,0,0,.1);
    border-radius: 15px;
    padding:15px;
    width: 100%;
    display: flex;
    flex-direction: column;
}
.header-menu__nav ul li ul li {
    margin:0;
}
.header-menu__nav ul li ul li a {
    border:none;
}


.header-menu__actions {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.header-menu__actions a {
    float: left;
    margin-left: 20px;
    position: relative
}

.header-menu__actions a span {
    position: absolute;
    width: 18px;
    height: 18px;
    border-radius: 8px;
    background: #d02333;
    color: #fff;
    font-size: 6pt;
    right: -10px;
    top: -10px
}

.header-menu__actions a:nth-child(1) {
    margin-left: 0
}

.header-menu__actions a svg {
    width: 28px;
    height: 28px
}

.header-menu__actions a svg path {
    stroke: #2f4858
}

.header-menu__actions a.srch svg {
    width: 26px;
    height: 26px
}

.header-menu__actions a:Hover svg path {
    stroke: #d02333
}

.burger-open svg {
    width: 26px!important;
    height: 26px!important;
}



.header-menu__actions a.login {
    color: #fff;

}

.header-menu__actions a.login svg {
    width: 30px;
    height: 30px
}

.header-menu__actions a.login svg path {
    stroke: #d02333
}

.header.fixed {
    position: fixed;
    top: 0;
    height: auto;
    background: #fff;
    -webkit-box-shadow: 0 0 30px rgba(1, 0, 0, .1);
    box-shadow: 0 0 30px rgba(1, 0, 0, .1);
    z-index: 900
}

.header.fixed .header-menu__logo svg {
    height: 80px;
    margin-left: -60px
}

.section {
    padding-top: 80px
}

.section-title {
    margin-bottom: 20px
}

.section-title h1 {
    font-size: 30px
}

.hero {
    height: calc(100vh - 360px);
    position: relative
}

.hero-slider__title {
    height: 500px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 50%;
    padding-right: 100px;
    float: left
}

.hero-slider__title span {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    letter-spacing: 5px;
    font-size: 16px;

    font-weight: 600px
}

.hero-slider__title span:before {
    content: "";
    width: 53px;
    height: 2px;
    background: #d02333;
    float: left;
    margin-right: 30px
}

.hero-slider__title h1 {
    margin-top: 0px;
    font-size: 42px;
    line-height: 1.7
}

.hero-slider__title h1 strong {
    color: #d02333
}

.hero-slider__title p {
    font-style: normal;
    font-weight: 300;
    font-size: 16px;
    line-height: 160%;
    margin-top: 10px;
    color: #28272c;
    opacity: .7;
    max-width:60%;
}

.hero-slider__title a {
    float: left;
    background: #2f4858;
    color: #fff;
    width: 168px;
    font-size: 16px;
    margin-top: 30px
}

.hero-slider__img {
    width: 50%;
    float: left;
    height: calc(100vh - 320px);
    border-radius: 15px;
    overflow: hidden
}

.hero-slider__img figure {
    height: 100%
}

.hero .counter {
    position: absolute;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 200px;
    bottom: 10px;
    right: 30px;
    font-size: 16px;
    color: #d02333
}

.swiper-progress-bar {
    width: 100%;
    height: 2px;
    position: relative;
    margin: 20px auto;
    background: rgba(1, 0, 0, .1)
}

.swiper-progress-bar .progress {
    height: inherit;
    left: 0;
    top: 0;
    position: absolute;
    background: #d02333;
    z-index: 1
}

.swiper-progress-bar .progress-sections {
    left: 0;
    top: 0;
    position: absolute;
    height: inherit;
    width: inherit;
    z-index: 2;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row
}

.swiper-progress-bar .progress-sections span {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    height: inherit;
    border-right: 2px solid #eee
}

.swiper-progress-bar .progress-sections span:last-child {
    border-right-width: 0
}

.swiper-counter {
    width: 100%
}

.import-logos {
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    background-image: -o-radial-gradient(#28272c 1px, transparent 0);
    background-image: radial-gradient(#28272c 1px, transparent 0);
    background-size: 40px 40px;
    padding: 50px 30px;
    padding-bottom: 10px
}

.import-logos__item {
    width: 33.3333333333%;
    float: left;
    margin-bottom: 40px
}

.import-logos__item a figure img {
    max-height: 60px
}

.import-title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-left: 100px
}

.import-title span {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    letter-spacing: 5px;
    font-size: 16px;

}

.import-title span:before {
    content: "";
    width: 53px;
    height: 2px;
    background: #2f4858;
    float: left;
    margin-right: 30px
}

.import-title h2 {
    margin-top: 30px;
    font-size: 32px;
    line-height: 130%
}

.import-title h2 strong {
    color: #d02333
}

.import-title p {
    font-style: normal;
    font-weight: 300;
    font-size: 18px;
    line-height: 160%;
    margin-top: 30px;
    color: #28272c;
    opacity: .7
}

.import-title a {
    float: left;
    background: #d02333;
    color: #fff;
    max-width: 192px;
    font-size: 16px;
    margin-top: 30px
}

.map {
    background: #28272c;
    height: 600px;
    position: relative;
    background-image: -o-radial-gradient(#fff 1px, transparent 0);
    background-image: radial-gradient(#fff 1px, transparent 0);
    background-size: 40px 40px;
    margin-top: 80px
}

.map-title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-right: 100px;
    color: #fff;
    height: 480px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.map-title span {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    letter-spacing: 5px;
    font-size: 16px;

}

.map-title span:before {
    content: "";
    width: 53px;
    height: 2px;
    background: #2f4858;
    float: left;
    margin-right: 30px
}

.map-title h2 {
    margin-top: 30px;
    font-size: 32px;
    line-height: 130%
}

.map-title h2 strong {
    color: #d02333
}

.map-item {
    width: 50%;
    float: right;
    height: 600px;
    overflow: hidden;
    position: absolute;
    right: 0;
    top: 0
}

.map-item figure {
    height: 600px
}

.map-item figure img {
    -o-object-fit: contain;
    object-fit: contain
}

.bg-img {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    opacity: .1;
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover
}

.stats {
    position: relative;
    padding-top:0;
}

.stats-items {
    border-radius: 30px;
    background:#FFF4F1;
    padding:50px;
}

.stats-title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-right: 100px
}

.stats-title span {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #fff;
    letter-spacing: 5px;
    font-size: 16px;

}

.stats-title span:before {
    content: "";
    width: 53px;
    height: 2px;
    background: #fff;
    float: left;
    margin-right: 30px
}

.stats-title h2 {
    margin-top: 30px;
    font-size: 32px;
    line-height: 130%;
    color: #fff
}

.stats-title h2 strong {
    color: #d02333
}

.stats-title p {
    font-style: normal;
    font-weight: 300;
    font-size: 18px;
    line-height: 160%;
    margin-top: 30px;
    color: #fff;
    opacity: .7
}

.stats-title a {
    float: left;
    background: #d02333;
    color: #fff;
    max-width: 192px;
    font-size: 16px;
    margin-top: 30px
}

.stats-items {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.stats-items__item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #d02333;
    width: 25%;
    font-size: 16px;

    border-right: 1px solid #ffe4de;
}

.stats-items__item:last-child {
    border-right: 0;
}

.stats-items__item figure {
    margin-bottom: 40px
}

.stats-items__item figure img {
    height: 60px
}

.stats-items__item figure svg path {
    fill: #fff
}

.stats-items__item figure svg rect {
    fill: #fff
}

.stats-items__item .number {
    font-weight: 700;
    font-size: 62px;
    line-height: 130%;
    text-align: center;
    letter-spacing: 10px;
    color: #2f4858;
    margin-bottom: 10px
}

.marquee-wall {
    overflow: hidden;position: relative;
}

.footer {
    margin-top: 120px;
    position: relative;
    background-color: #2f4858;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='199' viewBox='0 0 100 199'%3E%3Cg fill='%23213644' fill-opacity='0.4'%3E%3Cpath d='M0 199V0h1v1.99L100 199h-1.12L1 4.22V199H0zM100 2h-.12l-1-2H100v2z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E");

    padding-top: 60px;
    color: #fff
}

.footer-by {
    padding: 30px 0px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    color: #fff;
    justify-content: space-between;
    /*background: rgba(255,255,255,.1);*/
    /*border-top-left-radius: 10px;*/
    /*border-top-right-radius: 10px;*/
    margin-top: 30px;
}


.footer-by ul {
    display: flex;
    align-items: center;
}

.footer-by ul li {
    float:left;
    margin-left: 35px;
}

.footer-by ul li a {
    color: #fff;
    opacity: 0.7;
}

.footer-by ul li a:hover {
    opacity: 1;
}

.footer .bg-img {
    filter: blur(5px);
    background-attachment: revert;
    background-position: bottom;
    background-repeat: no-repeat;
}


.footer-by p a {
    color: #fff;
    text-decoration: underline
}

.footer-by p a:hover {
    color: #2f4858
}

.footer .footer-logo {
    padding-bottom: 50px;
    padding-left: 0;
    padding-right: 50px
}

.footer .footer-logo svg {
    height: 140px;
    margin-left: -40px;
    width: 200px
}

.footer .footer-logo svg path {
    fill: #fff
}

.footer .footer-logo p {
    color: #fff;
    opacity: .7;
    line-height: 1.7
}

.footer .footer-block h3 {
    color: #fff;

    opacity: 1;
    font-size: 16px;
    margin-bottom: 30px
}

.footer .footer-block ul li {
    margin-bottom: 10px
}

.footer .footer-block ul li:last-child {
    margin-bottom: 0
}

.footer .footer-block ul li a {
    color: #fff;
    opacity: .5;
}
.footer .footer-block ul li a span i {
    display: none;
}
.footer .footer-block ul li a:hover {
    color: #d02333;
    opacity: 1
}

.footer .footer-block .socials {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-top: 30px
}

.footer .footer-block .socials a {
    margin-right: 20px;
    float: left;
    color: #fff;
    font-size: 21px
}

.footer .footer-block .socials a:hover {
    color: #d02333
}

.about-header {
    position: relative;
    height: calc(100vh - 102px);
    margin-top:80px;
}

.about-header:after {
    content: "";
    position: absolute;
    width: 30%;
    right: 0;
    background: #f2f2f2;
    right: 0;
    top: -142px;
    height: calc(100% + 102px);
    z-index: -1;
    display: none;
}

.about-header__title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-right: 50px;
    height: calc(100vh - 102px);
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.about-header__title span {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    letter-spacing: 5px;
    font-size: 16px;

}

.about-header__title span:before {
    content: "";
    width: 53px;
    height: 2px;
    background: #2f4858;
    float: left;
    margin-right: 30px
}

.about-header__title h1 {
    margin-top: 30px;
    font-size: 32px;
    line-height: 130%
}

.about-header__title h1 strong {
    color: #d02333
}

.about-header__title p {
    font-style: normal;
    font-weight: 300;
    font-size: 18px;
    line-height: 160%;
    margin-top: 30px;
    color: #28272c;
    opacity: .7
}

.about-header__title a {
    float: left;
    background: #2f4858;
    color: #fff;
    max-width: 192px;
    font-size: 16px;
    margin-top: 30px
}

.about-header__img {
    width: 100%;
    height: calc(100vh - 162px);
}

.about-header__img figure {
    height: 100%;
    overflow: hidden;
    border-radius: 20px;
}

.section-title h2 {
    font-weight: 500;
    font-size: 24px;
    line-height: 130%;
    text-align: center
}

.section-title span {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-family: 'p_regular', sans-serif;
    max-width: 60%;
    font-size: 14px;
    font-family: 'p_regular', sans-serif;
    max-width: 60%;
    line-height: 1.5;

}


.countdowns .section-title span {
    width: 100%;
    float:left;
}

.section-title h1:before {
    content: "";
    width: 53px;
    height: 2px;
    background: #d02333;
    float: left;
    margin-right: 30px
}

.section-title h1 {
    margin-top: 10px;
    font-size: 24px;
    line-height: 130%;
    color: #d02333;
    margin-bottom: 10px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;

}

.section-title {
    display: flex;
    flex-direction: column-reverse;
}

.section-title h1 strong {
    color: #d02333
}

.section-title p {
    font-style: normal;
    font-weight: 300;
    font-size: 18px;
    line-height: 160%;
    margin-top: 30px;
    color: #28272c;
    opacity: .7;
    float: left;
    width:100%;
}


.countdowns .section-title p {
    font-size: 14px;
}

.section-title ul {
    margin-top: 40px
}

.section-title ul li {
    float: left;
    margin-right: 20px;
    margin-bottom: 20px
}

.section-title ul li:last-child {
    margin-right: 0
}

.section-title ul li a {
    color: #28272c;
    background: #f9f9f8;
    font-size: 14px;

    float: left;
    -webkit-transition: .3s all ease-in;
    -o-transition: .3s all ease-in;
    transition: .3s all ease-in
}

.section-title ul li a:hover {
    background: #d02333;
    color: #fff
}

.section-title ul li.active a {
    background: #d02333;
    color: #fff
}

.about-text__img {
    height: 800px;
    margin-top: 80px
}

.about-text__img figure {
    height: 100%
}

.about-text figcaption {
    margin-top: 80px
}

.about-text figcaption p {
    margin-bottom: 30px;
    line-height: 1.7;
    font-size: 16px;
    color: #28272c;
    opacity: .7
}

.gallery-block__item {
    margin-top: 30px
}

.gallery-block__item figure {
    height: 190px
}

.gallery-block__item a.view {
    height: 190px;
    background: rgba(87, 101, 253, .2);
    color: #5765fd
}

.industry {
    height: 80vh;
    position: relative
}

.industry-top {
    background: -webkit-gradient(linear, left bottom, left top, from(rgba(0, 0, 0, .5)), to(rgba(0, 0, 0, .5))), #f9f9f8;
    background: -o-linear-gradient(bottom, rgba(0, 0, 0, .5), rgba(0, 0, 0, .5)), #f9f9f8;
    background: linear-gradient(0deg, rgba(0, 0, 0, .5), rgba(0, 0, 0, .5)), #f9f9f8;
    height: 80vh;
    position: relative;
    overflow: hidden
}

.industry-top:after {
    width: 45%;
    height: 100%;
    background-image: -o-radial-gradient(#fff 1px, transparent 0);
    background-image: radial-gradient(#fff 1px, transparent 0);
    background-size: 60px 60px;
    content: "";
    position: absolute;
    right: -10px;
    top: 3%
}

.industry-top__title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    height: 80vh;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding-left: 50px;
    color: #fff
}

.industry-top__title span {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    letter-spacing: 5px;
    font-size: 16px;

}

.industry-top__title span:before {
    content: "";
    width: 53px;
    height: 2px;
    background: #2f4858;
    float: left;
    margin-right: 30px
}

.industry-top__title h1 {
    margin-top: 30px;
    font-size: 32px;
    line-height: 130%
}

.industry-top__title h1 strong {
    color: #d02333
}

.down {
    margin-top: -80px;
    background: #d02333;
    padding-top: 120px;
    padding-bottom: 60px;
    position: relative;
    z-index: -1
}

.down:before {
    content: "";
    width: 50%;
    height: 100%;
    background: #28272c;
    right: 0;
    bottom: 0;
    position: absolute
}

.down-history {
    position: relative;
    z-index: 300;
    padding-left: 100px;
    color: #fff
}

.down-history h3 {
    letter-spacing: .1em;

    margin-bottom: 30px
}

.down-history figcaption {
    line-height: 1.7;
    font-size: 14px;
    color: #fffffd
}

.down-list ul {
    padding: 0 50px
}

.down-list ul li {
    float: left;
    width: 33.3333333333%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    color: #fff;
    margin-bottom: 60px
}

.down-list ul li:nth-last-child(-n+3) {
    margin-bottom: 0
}

.down-list ul li span {
    font-size: 16px;
    margin-bottom: 20px;

}

.down-list ul li p {
    font-size: 14px
}

.i-brands__list {
    position: relative;
    background-image: -o-radial-gradient(#242424 1px, transparent 0);
    background-image: radial-gradient(#242424 1px, transparent 0);
    background-size: 60px 60px;
    padding: 50px;
    padding-bottom: 120px;
    padding-top: 120px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.i-brands__item {
    width: 16.6666666667%;
    margin-bottom: 120px
}

.i-brands__item figure img {
    max-height: 80px
}

.i-brands__item:nth-last-child(-n+6) {
    margin-bottom: 0
}

.in-brands__list {
    background-image: -o-radial-gradient(#242424 1px, transparent 0);
    background-image: radial-gradient(#242424 1px, transparent 0);
    background-size: 60px 60px;
    padding: 120px 50px
}

.in-brands__list-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 20%;
    margin-bottom: 80px
}

.in-brands__list-item:nth-last-child(-n+5) {
    margin-bottom: 0
}

.in-brands__list-item a {
    color: #d02333;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.in-brands__list-item figure {
    margin-bottom: 50px
}

.about-header-two {
    position: relative;
    height: calc(100vh - 102px)
}

.about-header-two:after {
    content: "";
    position: absolute;
    width: 30%;
    left: 0;
    background: #2f4858;
    right: 0;
    top: -102px;
    height: calc(100% + 102px);
    z-index: -1
}

.about-header-two__title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-left: 50px;
    height: calc(100vh - 102px);
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.about-header-two__title span {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    letter-spacing: 5px;
    font-size: 16px;

}

.about-header-two__title span:before {
    content: "";
    width: 53px;
    height: 2px;
    background: #2f4858;
    float: left;
    margin-right: 30px
}

.about-header-two__title h1 {
    margin-top: 30px;
    font-size: 32px;
    line-height: 130%
}

.about-header-two__title h1 strong {
    color: #d02333
}

.about-header-two__title p {
    font-style: normal;
    font-weight: 300;
    font-size: 18px;
    line-height: 160%;
    margin-top: 30px;
    color: #28272c;
    opacity: .7
}

.about-header-two__title a {
    float: left;
    background: #2f4858;
    color: #fff;
    max-width: 192px;
    font-size: 16px;
    margin-top: 30px
}

.about-header-two__img {
    width: 80%;
    height: calc(100vh - 162px);
    margin-left: 10%
}

.about-header-two__img figure {
    height: 100%;
    background: #f9f9f8
}

.about-header-two__img figure img {
    height: 120px
}

.blog-items__item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 100px
}

.blog-items__item figure {
    width: 40%;
    float: left;
    height: 600px;
    position: relative
}

.blog-items__item figure:before {
    content: "";
    width: 100%;
    height: 190px;
    left: -20%;
    background: #28272c;
    position: absolute;
    bottom: -80px;
    z-index: -1
}

.blog-items__item .title-in {
    width: 60%;
    float: left;
    padding-left: 100px
}

.blog-items__item .title-in span {
    color: #d02333;
    margin-bottom: 20px
}

.blog-items__item .title-in h2 {
    font-size: 32px;
    line-height: 1.7;
    margin-bottom: 20px
}

.blog-items__item .title-in p {
    font-size: 14px;
    line-height: 1.7;
    color: #28272c;
    opacity: .7
}

.blog-items__item .title-in a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    float: left;
    letter-spacing: 1px;
    font-size: 16px;
    margin-top: 30px;
    color: #28272c
}

.blog-items__item .title-in a:after {
    content: "";
    width: 32px;
    height: 2px;
    background: #d02333;
    float: right;
    margin-left: 30px
}

.blog-items__item .title-in a:hover {
    color: #d02333
}

.blog-items__item:nth-child(even) {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
    -ms-flex-direction: row-reverse;
    flex-direction: row-reverse
}

.blog-items__item:nth-child(even) .title-in {
    padding: 0;
    padding-right: 100px
}

.blog-items__item:nth-child(even) figure:before {
    left: 20%
}

.news-full {
    position: relative
}

.news-full:after {
    content: "";
    width: 50%;
    height: 80vh;
    background: #2f4858;
    position: absolute;
    right: 0;
    top: 0;
    z-index: -1
}

.news-full__img {
    height: 100vh
}

.news-full__img figure {
    height: 100%
}

.news-full__desc {
    margin-bottom: 40px
}

.news-full__desc .title-out {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.news-full__desc .title-out h1 {
    font-size: 32px;
    line-height: 1.7;
    color: #28272c;
    margin-bottom: 20px;
    text-align: center
}

.news-full__desc .title-out span {
    color: #d02333
}
.news-full__desc .title-out {
    margin-bottom: 40px;
}
.blog-content {
    margin-top: 40px;
    line-height: 1.7;
    color: #28272c;
    opacity: .7;
    font-size: 16px
}

.blog-content p {
    margin-bottom: 15px;
    line-height: 1.7;
    color: #28272c;
    opacity: .7;
    font-size: 16px
}

.contact {
    position: relative;
    height: calc(100vh - 102px)
}

.contact:before {
    content: "";
    width: 50%;
    height: 100%;
    position: absolute;
    left: 0;
    background: #d02333
}

.contact-title span {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    letter-spacing: 5px;
    font-size: 16px;

    color: #fff
}

.contact-title span:before {
    content: "";
    width: 53px;
    height: 2px;
    background: #2f4858;
    float: left;
    margin-right: 30px
}

.contact-title h1 {
    margin-top: 30px;
    font-size: 32px;
    line-height: 130%;
    color: #fff
}

.contact-title p {
    font-style: normal;
    font-weight: 300;
    font-size: 18px;
    line-height: 160%;
    margin-top: 30px;
    color: #fff
}

.contact form {
    padding-left: 50px;
    height: calc(100vh - 102px);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.contact form .form-group {
    margin-bottom: 40px
}

.contact form .form-group input {
    color: #28272c;
    opacity: .7;
    height: 38px;
    border-bottom: 1px solid rgba(40, 39, 44, .2);
    line-height: 38px
}

.contact form .form-group textarea {
    height: 110px;
    color: #28272c;
    opacity: .7;
    border-bottom: 1px solid rgba(40, 39, 44, .2);
    line-height: 38px
}

.contact form button {
    background: #2f4858;
    color: #fff;
    cursor: pointer
}

.about-item {
    padding: 30px;
    border-radius: 20px;
    background: #fff;
    -webkit-box-shadow: 5px 10px 15px #f2f2f2;
    box-shadow: 5px 10px 15px #f2f2f2;
    margin-top: 30px;
    position: relative
}

.about-item:before {
    width: calc(100% - 6px);
    height: calc(100% - 6px);
    position: absolute;
    border: 2px solid #d02333;
    left: 4px;
    top: 4px;
    content: "";
    border-radius: 20px;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: .3s all ease-in;
    -o-transition: .3s all ease-in;
    transition: .3s all ease-in
}

.about-item:hover:before {
    opacity: 1;
    visibility: visible
}

.about-item figure span {
    font-size: 44px;
    margin-bottom: 20px;
    color: #ffe4de;
    display: none
}

.about-item h2 {
    font-size: 18px;
    color: #28272c;
    line-height: 1.2;
    margin-top: 5px;
    margin-bottom: 10px;
    font-family: "p_regular";

}

.about-item h2 span {
    color: #d02333
}

.about-item p {
    color: #888;
    font-size:   14px;
    line-height: 1.7
}

.partners-item__carousel-item figure {
    border: 1px solid #f2f2f2;
    background: #fff;
    border-radius: 10px;
    padding: 20px 0
}

.partners-slider {
    margin-top: 40px;
}

.partners-item__carousel-item figure img {
    max-height: 36px;
    width: 100%;
    -o-object-fit: contain;
    object-fit: contain
}

.services {
    background: #ffe4de;
    padding: 80px 0;
    margin-top: 80px
}

.services.services-full {
    margin-top: 0;
    background: 0 0
}

.services-bg {
    position: absolute;
    top: 124%;
    right: 0;
    opacity: .1;
    width: 600px;
    height: 400px
}

.services-bg svg {
    width: 100%;
    height: 100%;
    fill: #d02333
}

.services-item {
    padding: 30px;
    background: #fff;
    border-radius: 15px;
    margin-top: 30px
}

.services-item figure {
    width: 100%;
    height: 400px;
    border-radius: 15px;
    background: #d02333;
    overflow: hidden;
}

.services-item figure img {
    height:100%;
    width: 100%;
    object-fit: cover;
}

.services-item figure svg path {
    fill: #fff
}

.services-item__title {
    margin-top: 30px
}

.services-item__title h2 {
    font-size: 16px;
    line-height: 1.7;
    margin-bottom: 15px;

}

.services-item__title p {
    color: #888;
    line-height: 1.7;
    margin-bottom: 15px
}

.services-item__title h1 , services-item__title h2 , .services-item__title h3 , .services-item__title h4 {
    width: 100%;
    float:left;
    margin-bottom: 15px;
}

.services-item__title a {
    color: #d02333;
    text-decoration: underline
}

.speadbar {
    width: 100%;
    float: left;
    padding: 15px 0;
    position: relative;
    z-index: 100;
    padding-top: 210px;
    /*margin-bottom: -80px;*/

}

.speadbar:before {
    content: "";
    background-color: #2f4858;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='199' viewBox='0 0 100 199'%3E%3Cg fill='%23213644' fill-opacity='0.4'%3E%3Cpath d='M0 199V0h1v1.99L100 199h-1.12L1 4.22V199H0zM100 2h-.12l-1-2H100v2z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E");
    width: 100%;
    height: 200px;
    position: absolute;
    top:0;
}

.speadbar-title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.speadbar-title h1 {
    color:#242424;

}

.new-title {
    position: absolute;
    top: 0;
    height: 350px;
    display: flex;
    align-items: center;
    top: -280px;
}

.new-title h1 {
    color: #fff;
    z-index: 300;
    font-size: 34px;
    font-family: 'p_bold', sans-serif;
    font-weight: 700;
}

.speadbar-title ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
}

.speadbar-title ul li {
    float: left;
    display: flex;
    align-items: center;
    color:#242424;
    font-size: 12px;
}
.speadbar-title ul li svg {
    width: 24px;
    height: 24px;
    margin-right: 20px;
}
.speadbar-title ul li svg path {
    stroke: #d02333;
}
.speadbar-title ul li:After {
    content: "/";
    margin: 0 20px;
    float: right;
    color: #242424
}

.speadbar-title ul li:first-child:after {
    display: none;
}

.speadbar-title ul li:last-child:after {
    display: none
}

.speadbar-title ul li a {
    color: #242424
}

.speadbar-title ul li.active {
    color: #d02333
}

.speadbar-title ul li.active a {
    color: #d02333
}

.speadbar-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    width: 100%
}

.speadbar-item h1 {
    font-size: 21px;

}

.speadbar-item ul li {
    float: left;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.speadbar-item ul li:last-child {
    color: #d02333
}

.speadbar-item ul li:After {
    content: "•";
    margin: 0 10px;
    float: right
}

.speadbar-item ul li:last-child:After {
    margin-right: 0;
    content: ""
}

.speadbar-item ul li a {
    color: #242424
}

.speadbar-item ul li.active a {
    color: #d02333
}

.courses .section-title span {
    color: #fff
}

.courses .section-title span:before {
    background: #fff
}

.courses .section-title h1 {
    color: #fff
}

.courses-slider {
    margin-top: 20px
}

.courses-slider__item {
    background: #fff;
    padding: 10px;
    border-radius: 15px;
    /*min-height: 86px;*/
}

.courses-slider__item a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #242424;
    transition: 0.3s all ease-in;
}

.courses-slider__item a figure {
    width: 60px;
    height: 60px;
    border: 1px solid #f2f2f2;
    border-radius: 15px;
    margin-right: 20px;
    transition: 0.3s all ease-in;
}

.courses-slider__item a figure img {
    width:36px;
}

.courses-slider__item a .title-in h2 {
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 1.5;
    font-family: "p_regular";
    font-weight: normal;
}

.courses-slider__item a .title-in span {
    color: #d02333
}


.courses-slider__item a:hover figure {
    border-color:  #d02333;
}
.courses-slider__item a:hover {
    color:  #d02333;
}

.filter-block {
    width: 100%;
    padding: 30px;
    float: left;
    position: sticky;
    top: 0;
    background: #fff;
    -webkit-box-shadow: 0 0 30px rgba(1, 0, 0, .1);
    box-shadow: 0 0 30px rgba(1, 0, 0, .1);
    border-radius: 15px
}

.filter-block__titletop {
    margin-bottom: 30px
}

.filter-block__titletop h1 {
    font-size: 21px;
    font-family: "p_regular";

}

.filter-block__titletop span {
    color: #d02333;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-top: 10px
}

.filter-block__titletop span svg {
    width: 18px;
    height: 18px;
    float: left;
    margin-right: 10px
}

.filter-block__titletop span svg path {
    stroke: #d02333 !important
}

.filter-block .btn-click {
    width: 100%;
    float: left;
    padding: 15px 0;
    text-align: center
}

.filter-block .title {
    font-size: 14px;

}

.filter-block__title {
    width: 100%;
    float: left;
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.filter-block__title:after {
    content: "";
    width: 60px;
    height: 2px;
    background: #d02333;
    position: absolute;
    right: 0;
    margin-top: -4px
}

.filter-block__title h1 {
    font-size: 14px;
    width: 100%;
    float: left;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    color: #242424;

}

.filter-block__title h1 a {
    color: #252a35;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-size: 14px;
    border-radius: 50%;
    width: 25px;
    margin-top: 1px;
    height: 25px;
    float: right
}

.filter-block .content-this {
    width: 100%;
    float: left;
    padding-bottom: 20px;
    margin-bottom: 10px;
    padding-top: 10px
}

.filter-block .content-this ul li a {
    width: 100%;
    float: left;
    padding: 10px 0;
    color: #777
}

.filter-block .content-this .hide-all, .filter-block .content-this .show-all {
    width: 100%;
    float: left;
    background: 0 0;
    font-size:   14px;
    color: #d02333;
    margin-top: 20px;
    text-align: center;
    cursor: pointer
}

.filter-block .content-this .hide-all {
    display: none
}

.filter-block .content-this .styled-checkbox {
    position: absolute;
    opacity: 0
}

.filter-block .content-this .styled-checkbox + label {
    position: relative;
    cursor: pointer;
    padding: 0;
    font-size: 12px
}

.max, .min {
    padding: 15px 0;
    text-align: center;
    color: #050708;
    border: 1px solid #f2f2f2;
    width: calc(50% - 10px);
    margin-right: 15px;
    margin-bottom: 20px
}

.max {
    margin-right: 0
}

.filter-block .content-this .styled-checkbox + label:before {
    content: "";
    margin-right: 10px;
    margin-top: -3px;
    display: inline-block;
    vertical-align: text-top;
    width: 20px;
    height: 20px;
    border: 1px solid #dedede
}

.filter-block .content-this .styled-checkbox:hover + label:before {
    background: #d02333
}

.filter-block .content-this .styled-checkbox:focus + label:before {
    -webkit-box-shadow: 0 0 0 3px rgba(0, 0, 0, .12);
    box-shadow: 0 0 0 3px rgba(0, 0, 0, .12)
}

.filter-block .content-this .styled-checkbox:checked + label:before {
    background: #d02333;
    border-color: #d02333
}

.filter-block .content-this .styled-checkbox:disabled + label {
    color: #b8b8b8;
    cursor: auto
}

.filter-block .content-this .styled-checkbox:disabled + label:before {
    -webkit-box-shadow: none;
    box-shadow: none;
    background: #ddd
}

.filter-block .content-this .styled-checkbox:checked + label:after {
    content: "";
    position: absolute;
    left: 5px;
    top: 6px;
    background: #fff;
    width: 2px;
    height: 2px;
    -webkit-box-shadow: 2px 0 0 #fff, 4px 0 0 #fff, 4px -2px 0 #fff, 4px -4px 0 #fff, 4px -6px 0 #fff, 4px -8px 0 #fff;
    box-shadow: 2px 0 0 #fff, 4px 0 0 #fff, 4px -2px 0 #fff, 4px -4px 0 #fff, 4px -6px 0 #fff, 4px -8px 0 #fff;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg)
}

.filter-block .content-this ul {
    float: left;
    margin-top: -10px;
    position: relative
}

.filter-block .content-this ul li {
    width: 100%;
    float: left;
    margin-top: 15px;
    font-size:   14px;

    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.filter-block .content-this ul li label {
    float: left
}

.filter-block .content-this ul li label span {
    float: right;
    font-size: 11px;
    color: #c4c4c4;
    margin-left: 7px;
    margin-top: 2px
}

.filter-block .content-this .without-hide {
    height: auto
}

.filter-block .content-this .without-hide:after {
    display: none
}

.filter-block .content-this .show-ul {
    height: auto
}

.filter-block .content-this .show-ul:after {
    display: none
}

.ui-slider-horizontal {
    height: 7px
}

.ui-slider-horizontal .ui-slider-handle {
    top: -5px
}

.ui-widget.ui-widget-content {
    background: #f2f2f2;
    border: none
}

.ui-widget-header {
    background: #d02333;
    border-color: #d02333;
    border: 1px solid #050708
}

.filter-form .ui-button, .filter-form .ui-state-default, .filter-form .ui-widget-content .ui-state-default, .filter-form .ui-widget-header .ui-state-default, html .filter-form .ui-button.ui-state-disabled:active, html .filter-form .ui-button.ui-state-disabled:hover {
    background: #fff;
    border-radius: 0;
    border-color: #d02333;
    border: 5px solid #d02333;
    -webkit-transform: skew(-10deg);
    -ms-transform: skew(-10deg);
    transform: skew(-10deg)
}

.colorss ul {
    width: 100%
}

.colorss ul li {
    width: calc(20% - 8px) !important;
    margin-right: 10px;
    float: left
}

.colorss ul li:nth-child(5n) {
    margin-right: 0
}

.regular-checkbox {
    display: none
}

.regular-checkbox + label {
    -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, .05), inset 0 -15px 10px -12px rgba(0, 0, 0, .05);
    box-shadow: 0 1px 2px rgba(0, 0, 0, .05), inset 0 -15px 10px -12px rgba(0, 0, 0, .05);
    padding: 9px;
    border-radius: 3px;
    display: inline-block;
    position: relative;
    cursor: pointer
}

.regular-checkbox + label:active, .regular-checkbox:checked + label:active {
    -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, .05), inset 0 1px 3px rgba(0, 0, 0, .1);
    box-shadow: 0 1px 2px rgba(0, 0, 0, .05), inset 0 1px 3px rgba(0, 0, 0, .1)
}

.regular-checkbox:checked + label {
    background-color: #cc9e5d
}

.regular-checkbox:checked + label:after {
    content: "\eed8";
    font-family: Icofont;
    font-size: 10px;
    position: absolute;
    top: 0;
    left: 3px;
    color: #fff
}

.big-checkbox + label {
    padding: 18px
}

.big-checkbox:checked + label:after {
    font-size: 21px;
    left: 8px;
    top: 8px;
    border: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: 0
}

.products-list {
    padding: 0;
    background: 0 0;
    margin: 0
}

.products-list:before {
    background: 0 0
}

.products-list .products-row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    width: 100%;
    padding-left: 30px
}

.products-list .products-row__item {
    width: calc(50% - 23px);
    margin-right: 45px;
    margin-bottom: 15px
}

.products-list .products-row__item:nth-child(2n) {
    margin-right: 0
}

.products-list .products-row__item .imgs {
    height: 350px
}

.btn-click {
    background: #d02333;
    border-radius: 5px;
    color: #fff;
    cursor: pointer;
    margin-bottom: 15px
}

.select2-container, .selection {
    width: 100% !important
}

.select2-container--default .select2-selection--single {
    background-color: #fff;
    border: 2px solid #f2f2f2;
    width: 100%;
    float: left;
    height: 46px;
    border-radius: 5px
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 42px;
    padding: 0 20px;
    font-size: 12px;
    color: #050708
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 26px;
    position: absolute;
    top: 9px;
    right: 20px;
    width: 20px
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background: #d02333;
    color: #fff
}

.select2-results__option {
    padding: 15px;
    font-size: 12px;
    cursor: pointer;
    line-height: 1.7
}

.allsize {
    height: 250px;
    overflow: hidden;
    position: relative
}

.allsize.showsizes {
    height: auto !important;
    overflow: hidden;
    position: relative
}

.allsize:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 80px;
    bottom: 0;
    z-index: 350;
    background: rgba(0, 0, 0, 0);
    background: -webkit-gradient(left top, left bottom, color-stop(0, rgba(0, 0, 0, 0)), color-stop(100%, #fff));
    background: -o-linear-gradient(top, rgba(0, 0, 0, 0) 0, #fff 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0, rgba(0, 0, 0, 0)), to(#fff));
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0, #fff 100%)
}

.allsize.showsizes:before {
    display: none
}

.load-more-sizes {
    width: 100%;
    float: left;
    padding: 15px 0;
    text-align: center;
    padding-top: 25px;
    font-size: 12px;
    color: #111;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.load-more-sizes span {
    width: 25px;
    height: 25px;
    text-align: center;
    color: #111;
    margin-right: 15px;
    border: 1px solid #111;
    font-size: 10pt;
    line-height: 1.75px;
    border-radius: 50%
}

.load-more-sizes:hover {
    color: #d02333
}

.load-more-sizes:hover span {
    color: #d02333;
    border-color: #d02333
}

.load-less-sizes {
    width: 100%;
    float: left;
    padding: 15px 0;
    text-align: center;
    padding-top: 25px;
    font-size: 12px;
    color: #111;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.load-less-sizes span {
    width: 25px;
    height: 25px;
    text-align: center;
    color: #111;
    margin-right: 15px;
    border: 1px solid #111;
    font-size: 10pt;
    line-height: 1.75px;
    border-radius: 50%
}

.load-less-sizes:hover {
    color: #d02333
}

.load-less-sizes:hover span {
    color: #d02333;
    border-color: #d02333
}

.courses-filters {
    width: 300px;
    margin-top: 40px;
    float: left;
    position: sticky;
    top:100px;
}

.courses-filters .cats {
    margin-top: 20px !important
}

.courses-filters .cats li {
    margin: 0 !important;
    margin-bottom: 10px !important
}

.courses-filters .cats li:last-child {
    margin-bottom: 0 !important
}

.courses-filters .cats li a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 0 !important
}

.courses-filters .cats li a figure {
    width: 45px;
    height: 45px;
    border: 1px solid #f2f2f2;
    border-radius: 15px;
    margin-right: 20px
}

.courses-filters .cats li a figure img {
    height: 15px
}

.courses-filters .cats li a h3 {
    font-size:   15px
}

.courses-items {
    width: calc(100% - 300px);
    padding-left: 30px;
    margin-top: 40px;
    float: left
}

.btn-reg {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background: #d02333;
    border-radius: 15px;
    color: #fff;
    transition: 0.3s all ease-in;
}

.btn-reg:hover {
    background: #2f4858;
    color:#fff!important;
}

.btn-reg svg {
    width: 21px;
    height: 21px;
    margin-right: 15px
}

.btn-reg svg path {
    stroke: #fff
}

.l {
    width: 100%;
    float: left;
    margin-bottom: 30px
}

.l-item {
    position: relative;
    background: #fff;
    -webkit-box-shadow: 0 0 30px rgba(1, 0, 0, .1);
    box-shadow: 0 0 30px rgba(1, 0, 0, .1);
    padding: 30px;
    border-radius: 15px;
    overflow: hidden
}

.l-item__title {
    width: 70%;
    float: left;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
}

.l-item__title span {
    color: #d02333;
    margin-bottom: 10px
}

.l-item__title h2 a {
    color: #242424;
    font-family: "p_regular";
    line-height: 1.5;
}

.filter-block__title h1 {
    font-family: "p_regular";
}



.l-item__title h2 a:hover {
    color: #d02333
}

.l-item__title ul {
    margin-top: 15px
}

.l-item__title ul li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px
}

.l-item__title ul li:last-child {
    margin-bottom: 0
}

.l-item__title ul li figure {
    width: 50px;
    height: 50px;
    border: 1px solid #f2f2f2;
    border-radius: 10px;
    margin-right: 15px
}

.l-item__title ul li figure svg {
    width: 25px;
    height: 25px
}

.l-item__title ul li p {

    margin-bottom: 5px;
    color: #888
}

.l-item__title a {
    margin-top: 20px
}

.l-item__title a:hover {
    color: #2f4858
}

.l-item__img {
    width: 30%;
    float: left;
    height: 100%;
    position: absolute;
    right: 0;
    overflow: hidden;
    top: 0
}

.l-item__img figure {
    height: 100%;
    background: #2f4858
}

.l-item__img figure img {
    opacity: .3
}

.l-item__img span {
    position: absolute;
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
    color: #fff;
    top: 50px;
    right: -10px;

    font-size: 18px;
    letter-spacing: 2px
}

.l-item__img .price {
    position: absolute;
    bottom: 20px;
    right: 20px;
    color: #fff;
    font-size: 24px;
    color: #fff
}

.courses-full {
    position: relative
}

.courses-full:before {
    content: "";
    width: 100%;
    height: 80px;
    background: #f2f2f2;
    position: absolute;
    top: 0;
    display: none;
}

.courses-full__title h1 {
    margin-top: 30px;

    color: #d02333;
    font-size: 36px
}

.courses-full__infos {
    padding: 15px 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.courses-full__infos ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.courses-full__infos ul li {
    float: left;
    margin-right: 40px;
    position: relative
}

.courses-full__infos ul li a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #242424
}

.courses-full__infos ul li a figure {
    width: 30px;
    height: 30px;
    overflow: hidden;
    border-radius: 50%;
    margin-right: 20px
}

.courses-full__infos ul li a svg {
    width: 24px;
    height: 24px;
    margin-right: 20px
}

.courses-full__infos ul li a span {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.courses-full__infos ul li a span svg {
    margin-right: 0;
    margin-left: 20px
}

.courses-full__infos ul li ul {
    position: absolute;
    width: 100%;
    top: 100%;
    margin-top: 20px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    background: #fff;
    padding: 15px;
    -webkit-box-shadow: 0 0 30px rgba(1, 0, 0, .1);
    box-shadow: 0 0 30px rgba(1, 0, 0, .1);
    border-radius: 15px;
    z-index: 400;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: .3s all ease-in;
    -o-transition: .3s all ease-in;
    transition: .3s all ease-in
}

.courses-full__infos ul li ul li {
    margin-bottom: 15px;
    margin-right: 0
}

.courses-full__infos ul li ul li:last-child {
    margin-bottom: 0
}

.courses-full__infos ul li ul li label {
    padding-left: 10px
}

.courses-full__infos ul li:hover ul {
    opacity: 1;
    visibility: visible
}

.courses-full__tabs {
    margin-top: 40px
}

.courses-full__tabs .nav-tabs {
    border: none
}

.courses-full__tabs .nav-tabs li a {
    padding: 15px 30px;
    border: none;
    outline: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 15px;
    color: #242424;
    font-size:18px;
}

.courses-full__tabs .nav-tabs li a svg {
    width: 24px;
    height: 24px;
    float: left;
    margin-right: 15px
}

.courses-full__tabs .nav-tabs li a.active {
    background: #f2f2f2;
    color: #242424;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.courses-full__tabs .nav-tabs li a.active svg {
    fill: #242424
}

.courses-full__tabs .nav-tabs li a.active svg path {
    stroke: #242424
}

.courses-full__tabs .tab-content .tab-pane {
    background: #f2f2f2;
    padding: 30px;
    border-radius: 15px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.courses-full__tabs .tab-content .tab-pane .content-in p {
    margin-bottom: 20px;
    line-height: 1.7;
    color: #242424
}

.courses-full__tabs .tab-content .tab-pane .content-in h3 {
    margin-bottom: 15px;
    color: #242424;
    font-family: "p_regular";
    font-weight: bold;
}.courses-full__tabs .tab-content .tab-pane .content-in h1 {
    margin-bottom: 15px;
    color: #242424;
    font-family: "p_regular";
    font-weight: bold;
}.courses-full__tabs .tab-content .tab-pane .content-in h2 {
    margin-bottom: 15px;
    color: #242424;
    font-family: "p_regular";
    font-weight: bold;
}

.courses-full__tabs .tab-content .tab-pane .content-in ul {
    padding-left: 15px;
    width: 100%;
    float:left;
    margin-bottom: 15px;
}

.courses-full__tabs .tab-content .tab-pane .content-in ul li {
    margin-bottom: 5px;
  
    line-height: 1.7
}

.courses-full__tabs .tab-content .tab-pane .content-in ul li:before {
    content: "\eaa0";
    width: 20px;
    font-family: Icofont;
    height: 20px;
    float: left;
    margin-right: 15px;
    background: #2f4858;
    border-radius: 50%;
    color: #fff;
    line-height: 21px;
    padding-left: 2px;
}

.courses-full__tabs .tab-content .tab-pane .content-in ul li:last-child {
    margin-bottom: 0
}

.courses-full__tabs .tab-content .tab-pane .btns-in {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin-top: 50px
}

.courses-full__tabs .tab-content .tab-pane .btns-in a {
    float: left;
    margin-right: 30px;
    border: 2px solid #dedede;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #242424
}

.courses-full__tabs .tab-content .tab-pane .btns-in a:last-child {
    margin-right: 0
}

.courses-full__tabs .tab-content .tab-pane .btns-in a svg {
    width: 24px;
    height: 24px;
    float: left;
    margin-right: 15px
}

.courses-full__tabs .tab-content .tab-pane .btns-in a:hover {
    border-color: #d02333;
    color: #d02333
}

.courses-full__admin {
    background: #2f4858;
    color: #fff;
    padding: 30px;
    border-radius: 15px;
    position: sticky;
    margin-top: 50px;
    top:0;
}

.courses-full__admin .us-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    text-align: center
}

.courses-full__admin .us-item__img {
    width: 210px;
    height: 210px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #d02333;
    padding: 5px
}

.courses-full__admin .us-item__img figure {
    height: 100%;
    border-radius: 50%;
    overflow: hidden
}

.courses-full__admin .us-item__title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-top: 30px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    text-align: center
}

.courses-full__admin .us-item__title h3 {
    margin-bottom: 20px;
    font-size: 21px
}

.courses-full__admin .us-item__title h4 {
    margin-bottom: 15px
}

.courses-full__admin .us-item__title h4 a {
    color: #fff
}

.courses-full__admin .tx {
    margin-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, .1);
    padding-top: 30px
}

.courses-full__admin .tx h3 {
    margin-bottom: 20px
}

.courses-full__admin .tx p {
    line-height: 1.7;
    opacity: .8
}

.courses-full__admin .icons ul {
    margin-top: 30px
}

.courses-full__admin .icons ul li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 20px
}

.courses-full__admin .icons ul li:last-child {
    margin-bottom: 0
}

.courses-full__admin .icons ul li img {
    margin-right: 20px;
    float: left;
    width: 40px;
    height: 40px
}

.cover {
    height: 180px;
    overflow: hidden;
    background: #242424;
    position: relative
}

.cover-ul {
    position: absolute;
    top: 30px;
    z-index: 300
}

.cover-ul li {
    float: left;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    list-style-type: none;
    color: #fff
}

.cover-ul li:after {
    content: "/";
    float: left;
    margin: 0 10px
}

.cover-ul li:last-child:after {
    display: none
}

.cover-ul li a {
    color: #fff
}

.cover img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    opacity: .3
}

.loginform {
    width: 100%;
    float: left;
    padding: 30px;
    margin-bottom: 50px;
    border-radius: 5px
}

.login-form {
    width: 450px;
    margin: auto
}

.login-form h1 {
    text-align: center;
    font-size: 21px;
    margin-bottom: 50px;
    color: #d02333
}

.loginform h3 {
    position: relative;
    width: 100%;
    float: left;
    margin: 30px 0 20px;
    font-size:   14px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    color: #d02333
}

.loginform h3:before {
    left: 0
}

.loginform h3:after {
    right: 0
}

.loginform h3:after, .loginform h3:before {
    content: "";
    position: absolute;
    width: 40%;
    height: 2px;
    background-color: #f2f2f2
}

.loginform-form {
    width: 100%;
    float: left;
    margin-bottom: 20px
}

.loginform-form label {
    width: 100%;
    position: relative;
    margin-bottom: 15px;
    font-size: 12px;
    color: #d02333
}

.loginform-form input {
    width: 100%;
    border: 2px solid rgba(1, 0, 0, .1);
    padding: 20px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 10px;
    color: #d02333;
    -webkit-transition: .4s all ease-in;
    -o-transition: .4s all ease-in;
    transition: .4s all ease-in
}

.loginform-form textarea {
    width: 100%;
    border: 2px solid rgba(1, 0, 0, .1);
    padding: 20px;
    font-size: 12px;
    min-height: 100px;
    font-weight: 500;
    border-radius: 10px;
    color: #d02333;
    -webkit-transition: .4s all ease-in;
    -o-transition: .4s all ease-in;
    transition: .4s all ease-in
}

.loginform-form input.error {
    border-color: #d02333 !important
}

.loginform-form input:focus {
    border-color: #d02333
}

.loginform-remember {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.loginform-remember a {
    color: #d02333;
    font-size: 12px
}

.loginform-btns {
    margin-top: 20px;
    width: 100%
}

.loginform .custom-control-label {
    font-size: 12px;
    margin-top: 6px;
    margin-left: 5px;
    color: #242424
}

.loginform .custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
    background: #d02333
}

.userloginform-buttons__social ul {
    width: 100%;
    float: left;
    margin-top: 10px
}

.userloginform-buttons__social ul, .userloginform-buttons__social ul li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.userloginform-buttons__social ul li {
    height: 45px;
    border-radius: 10px;
    border: 1px solid #dedede;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    line-height: 48px;
    font-size: 16px;
    margin: 0 5px;
    text-align: center;
    padding: 0 25px;
    background: #d34836;
    border-color: #d34836
}

.userloginform-buttons__social ul li a {
    color: #fff;
    font-size: 12px;
    padding: 0;

}

.userloginform-buttons__social ul li a i {
    font-size: 18px;
    float: left;
    margin-right: 10px;
    margin-top: 15px
}

.userloginform-buttons__social ul li:nth-child(2) {
    background: #3b5998;
    border-color: #3b5998
}

.userloginform-buttons__social ul li:last-child {
    background: #0072b1;
    border-color: #0072b1
}

.userloginform-buttons__social ul li:last-child a {
    color: #fff
}

.btn-submit {
    outline: 0;
    border: none;
    border-radius: 10px;
    padding: 20px 0;
    width: 100%;
    color: #fff;
    background: #d02333;
    cursor: pointer;
    font-size: 15px
}

.btn-reset {
    outline: 0;
    border: none;
    border-radius: 10px;
    padding: 20px;
    color: #fff;
    background: #d02333;
    cursor: pointer;
    font-size: 15px;
    width: 200px;
    border-radius: 35px;
    text-align: center;
    margin-top: 30px
}

.btn-register {
    outline: 0;
    border: none;
    border-radius: 10px;
    padding: 20px 0;
    width: 100%;
    float: left;
    color: #28272c;
    background: #f2f2f2;
    margin-top: 15px;
    cursor: pointer;
    font-size: 15px;
    text-align: center
}

.btn-register:hover {
    color: #d02333
}

.alert {
    padding: 20px 30px;
    font-size:   15px
}

.alert-success {
    background: #d02333;
    border-color: #d02333;
    color: #fff
}

.alert-danger {
    background: #d9534f;
    border-color: #d9534f;
    color: #fff
}

.login {
    padding-bottom: 0
}

.pricing-section {
    position: relative;
    padding: 100px 0 70px
}

.pricing-tabs {
    position: relative
}

.pricing-tabs .tab-buttons {
    text-align: center;
    position: relative;
    margin-bottom: 30px
}

.pricing-tabs .tab-buttons h4 {
    display: block;
    font-weight: 500;
    font-size: 16px;
    line-height: 1.72px;
    color: #202124;
    margin-bottom: 15px
}

.pricing-tabs .tab-btns {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.pricing-tabs .tab-btns:before {
    position: absolute;
    top: 0;
    left: 50%;
    width: 60px;
    height: 30px;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
    background: #d02333;
    border: 1px solid #e7e7ec;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-box-shadow: 0 8px 15px rgba(140, 152, 164, .1);
    box-shadow: 0 8px 15px rgba(140, 152, 164, .1);
    border-radius: 30px;
    content: "";
    -webkit-transition: all .3s ease;
    -o-transition: all .3s ease;
    -webkit-transition: all .3s linear;
    -o-transition: all .3s linear;
    transition: all .3s linear
}

.pricing-tabs .tab-btns li {
    position: relative;
    font-size: 14px;
    line-height: 30px;
    color: #696969;
    cursor: pointer;
    z-index: 8;
    padding: 0 48px 0;
    min-width: 196px;
    -webkit-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease
}

.pricing-tabs .tab-btns li:before {
    position: absolute;
    right: -26px;
    height: 20px;
    width: 20px;
    top: 5px;
    background: #fff;
    content: "";
    border-radius: 50%;
    -webkit-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease
}

.pricing-tabs .tab-btns li.active-btn:before {
    right: 6px
}

.pricing-tabs .tab-btns li:last-child:before {
    display: none
}

.pricing-table {
    position: relative;
    margin-bottom: 30px
}

.pricing-table .inner-box {
    position: relative;
    border: 1px solid rgba(255, 255, 255, .1);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 8px;
    padding: 40px 40px;
    overflow: hidden;
    -webkit-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease
}

.pricing-table .inner-box:hover, .pricing-table.tagged .inner-box {
    border: 1px solid #d02333;
    -webkit-box-shadow: 0 6px 15px rgba(64, 79, 104, .05);
    box-shadow: 0 6px 15px rgba(64, 79, 104, .05)
}

.pricing-table .tag {
    position: absolute;
    right: 30px;
    top: 30px;
    overflow: hidden;
    border-radius: 30px;
    font-size:   14px;
    line-height: 1.70px;
    color: #d02333;
    padding: 5px 25px
}

.pricing-table .tag:before {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background: #d02333;
    opacity: .15;
    content: "";
    border-radius: 30px
}

.pricing-table .title {
    display: block;
    font-weight: 500;
    font-size: 18px;
    line-height: 1.74px;
    color: #d02333;
    margin-bottom: 10px
}

.pricing-table .price {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end;
    font-size: 30px;
    line-height: 41px;
    color: #fff;
    font-weight: 500;
    margin-bottom: 50px;
    -webkit-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease
}

.pricing-table .duration {
    position: relative;
    font-size: 18px;
    line-height: 1em;
    color: gray;
    bottom: 7px;
    margin-left: 7px
}

.pricing-table .table-content {
    position: relative;
    margin-bottom: 60px
}

.pricing-table .table-content ul {
    position: relative
}

.pricing-table .table-content ul li {
    position: relative;
    font-size: 14px;
    line-height: 1.75px;
    color: #fff;
    font-weight: 400;
    margin-bottom: 20px;
    padding-left: 30px
}

.pricing-table .table-content ul li:before {
    position: absolute;
    left: 0;
    top: 0;
    font-family: Icofont;
    font-size: 16px;
    color: #d02333;
    content: "\eac7"
}

.pricing-table .table-footer {
    position: relative
}

.pricing-table .table-footer a {
    position: relative;
    display: block;
    width: 100%;
    background: #d02333;
    border-radius: 10px;
    text-align: center;
    color: #fff;
    padding: 17px
}

.smallmodal .modal-dialog {
    max-width: 536px;
    border: none;
    border-radius: 20px;
    padding: 0;
    overflow: hidden
}

.modal-content {
    border-radius: 20px;
    overflow: hidden
}

.smallmodal .smallbody {
    padding: 40px 30px
}

.smallmodal .modal-header {
    border: none;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding-top: 40px;
    padding-left: 30px
}

.smallmodal .modal-header h1 {
    width: 100%;
    float: left;
    font-size: 19px;
    text-align: left;
    padding-right: 30px
}

.smallmodal .close {
    width: 50px;
    height: 50px;
    background: #d02333;
    border-radius: 50%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    color: #fff;
    opacity: 1;
    font-size: 16px;
    text-shadow: none;
    right: 40px;
    top: 40px;
    position: absolute
}

.claim-text {
    padding: 0 15px;
    padding-bottom: 30px
}

.claim-text p {
    font-size: 14px;
    line-height: 1.7;
    color: gray
}

.claim-text a {
    background: #d02333;
    color: #fff;
    margin-top: 20px
}

.claim-text a:hover {
    background: #28272c;
    color: #fff
}

.pricing-section .nav-tabs {
    border: none;
    margin-bottom: 30px
}

.pricing-section .nav-tabs li {
    border: none;
    outline: 0;
    -webkit-box-shadow: none;
    box-shadow: none
}

.pricing-section .nav-tabs li button {
    color: #fff;
    border: none;
    outline: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    cursor: pointer;
    font-size: 15px;
    padding: 20px 50px
}

.pricing-section .nav-tabs li button.active {
    background: #d02333;
    color: #fff;
    border-radius: 10px
}

.profile-tab {
    width: 100%;
    float: left;
    padding: 30px 0;
    background: #2f4858;
    z-index: 400
}

.profile-tab .nav-tabs {
    width: 100%;
    float: left;
    border: none;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.profile-tab .nav-tabs .nav-item {
    margin-left: 0;
    margin-right: 40px
}

.profile-tab .nav-tabs .nav-item:last-child {
    margin-right: 0
}

.profile-tab .nav-tabs .nav-link {
    font-size:   14px;
    color: #fff;
    padding: 15px 30px;
    border: none;
    border-radius: 10px;
    margin-right: 0
}

.profile-tab .nav-tabs .nav-item.show .nav-link, .profile-tab .nav-tabs .nav-link.active {
    border-color: #d02333;
    background: #d02333;
    color: #fff
}

canvas {
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    font-family: BPGLarge !important
}

.content-list {
    width: 100%;
    float: left;
    margin-top: 15px
}

.content-list__img {
    float: left;
    width: 150px;
    height: 100px;
    overflow: hidden;
    border-radius: 5px;
    position: relative
}

.content-list__img figure {
    width: 150px;
    height: 100px
}

.content-list__img .product-card__badges {
    position: absolute;
    left: 5px;
    top: 5px
}

.content-list__img .vip-status {
    left: 10px;
    top: 15px
}

.content-list__img img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover
}

.content-list__desc {
    padding: 0 15px;
    width: calc(100% - 150px);
    float: right
}

.content-list__desc h1 {
    font-size: 16px
}

.content-list__desc h1 a {
    color: #d02333;
    font-family: Mbold !important
}

.content-list__desc ul {
    width: 100%;
    float: left;
    margin-top: 7px
}

.content-list__desc ul li {
    float: left;
    font-size: 12px;
    color: #2f4858;
    margin-right: 10px
}

.content-list__desc ul li:last-child {
    margin-right: 0
}

.content-list__desc ul li span {
    float: left;
    margin-right: 5px
}

.content-list__desc ul li u {
    text-decoration: none
}

.content-list__desc .address {
    float: left;
    width: 100%;
    font-family: BPGLarge;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-top: 10px
}

.content-list__desc .address span {
    float: left;
    width: 25px;
    height: 25px;
    margin-right: 10px;
    overflow: hidden;
    background: #d02333;
    font-size: 11px;
    color: #fff;
    border-radius: 50%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.content-list__desc .address p {
    font-size: 12px;
    font-family: BPGLarge;
    color: #252a35
}

.content-profile__item {
    width: 100%;
    float: left;
    padding: 30px;
    background: #fff;
    border-radius: 5px;
    margin-bottom: 30px;
    -webkit-box-shadow: 0 0 30px rgba(1, 0, 0, .1);
    box-shadow: 0 0 30px rgba(1, 0, 0, .1)
}

.content-profile__item h1 {
    font-family: Mbold;
    font-size: 16px;
    color: #252a35;
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    float: left
}

.content-profile__item h1 a {
    color: #d02333;
    font-family: BPGLarge;
    font-size: 14px
}

.content-profile__content {
    margin-top: 15px;
    float: left;
    width: 100%
}

.content-profile__content .chartjs-size-monitor {
    font-family: BPGLarge;
    color: #252a35;
    font-size:   15px
}

.content-profile__content-alert {
    width: 100%;
    float: left;
    padding: 15px 0
}

.content-profile__content-alert h2 {
    width: 100%;
    float: left;
    font-family: BPGLarge
}

.content-profile__content-alert a {
    float: left;
    padding: 15px 30px;
    background: #f2f2f2;
    font-family: BPGLarge;
    font-size:   14px;
    color: #252a35;
    border-radius: 5px;
    margin-top: 15px
}

.content-profile__content .this-ul {
    width: 100%;
    float: left
}

.content-profile__content .this-ul li {
    width: 100%;
    font-family: Mbold;
    font-size: 14px;
    float: left;
    padding: 10px 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-top: 5px;
    border-bottom: 1px dashed #ececec
}

.content-profile__content .this-ul li span {
    width: 40px;
    height: 40px;
    background: #d02333;
    border-radius: 50%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #fff;
    float: left;
    margin-right: 15px
}

.content-profile__content .this-ul li a {
    color: #d02333
}

.dashboard {
    padding: 40px 0
}

.dashboard-header {
    width: 100%;
    float: left
}

.dashboard-header__balance, .dashboard-header__name, .dashboard-header__wishlist {
    float: left;
    width: calc(33.3333333333% - 14px);
    margin-right: 20px;
    background: #fff;
    border-radius: 5px;
    -webkit-box-shadow: 10px 15px 30px rgba(1, 0, 0, .1);
    box-shadow: 10px 15px 30px rgba(1, 0, 0, .1);
    margin-top: 30px;
    margin-bottom: 30px;
    padding: 30px
}

.dashboard-header__wishlist {
    margin-right: 0
}

.dashboard-header__name .pimage {
    width: 80px;
    height: 80px;
    overflow: hidden;
    border-radius: 50%;
    float: left
}

.dashboard-header__name .ptitle {
    width: calc(100% - 80px);
    height: 80px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding-left: 30px
}

.dashboard-header__name .ptitle h2 {
    font-size: 14px
}

.dashboard-header__name .ptitle a {
    font-size: 12px;
    display: inline-block;
    margin-top: 10px;
    color: #d02333
}

.dashboard-header__balance .pimage {
    width: 80px;
    height: 80px;
    overflow: hidden;
    border-radius: 50%;
    float: left
}

.dashboard-header__balance .pimage figure {
    width: 100%;
    height: 100%;
    border: 1px solid #ececec;
    border-radius: 50%
}

.dashboard-header__balance .pimage figure a {
    width: 100%;
    height: 100%;
    color: #2f4858;
    font-size: 16px
}

.dashboard-header__balance .pimage figure a svg {
    width: 21px;
    height: 21px;
    fill: #2f4858
}

.dashboard-header__balance .ptitle {
    width: calc(100% - 80px);
    height: 80px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding-left: 30px
}

.dashboard-header__balance .ptitle h2 {
    font-size: 14px
}

.dashboard-header__balance .ptitle p {
    font-size: 12px;
    display: inline-block;
    margin-top: 10px;
    color: #d02333
}

.dashboard-header__wishlist .pimage {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    float: left;
    position: relative
}

.dashboard-header__wishlist .pimage span {
    position: absolute;
    width: 30px;
    height: 30px;
    background: #d02333;
    border-radius: 50%;
    top: 0;
    right: 0;
    color: #fff;
    font-size: 11px;
    line-height: 30px;
    text-align: center
}

.dashboard-header__wishlist .pimage figure {
    width: 100%;
    height: 100%;
    overflow: hidden;
    border: 1px solid #ececec;
    border-radius: 50%
}

.dashboard-header__wishlist .pimage figure a {
    width: 100%;
    height: 100%;
    color: #2f4858;
    font-size: 16px
}

.dashboard-header__wishlist .pimage figure a svg {
    width: 24px;
    height: 24px;
    fill: #d02333
}

.dashboard-header__wishlist .ptitle {
    width: calc(100% - 80px);
    height: 80px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding-left: 30px
}

.dashboard-header__wishlist .ptitle h2 {
    font-size: 14px
}

.dashboard-header__wishlist .ptitle p {
    font-size: 12px;
    display: inline-block;
    margin-top: 10px
}

.dashboard-header__wishlist .ptitle p a {
    color: #d02333
}

.dashboard-content__title {
    padding: 30px 0;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.dashboard-content__title h3 {
    color: #d02333;
    font-size: 21px
}

.dashboard-content__title a {
    color: #d02333
}

.dashboard-content__history ul {
    width: 100%;
    float: left
}

.dashboard-content__history ul li {
    width: 98%;
    float: left;
    padding: 15px 30px;
    border-radius: 5px;
    border: 2px solid rgba(1, 0, 255, .1);
    border-radius: 10px;
    -webkit-transition: all .3s ease-in;
    -o-transition: all .3s ease-in;
    transition: all .3s ease-in;
    margin-bottom: 30px
}

.dashboard-content__history ul li a {
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    color: #2f4858;

    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.dashboard-content__history ul li a span {
    width: 40px;
    height: 40px;
    background: #d02333;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #fff;
    border-radius: 50%
}

.dashboard-content__history ul li:last-child {
    margin: 0
}

.dashboard-content__history ul li:nth-child(even) {
    margin-left: 2%;
    width: 98%;
    background: rgba(255, 255, 255, .1)
}

.dashboard-content__history ul li:hover {
    background: #d02333;
    border-color: #d02333
}

.dashboard-content__history ul li:hover a {
    color: #242424
}

.dashboard-content__history ul li:hover a span {
    background: #d02333;
    color: #d02333
}

.dashboard .btn-click {
    width: 200px;
    padding: 20px 30px;
    border-radius: 5px
}

.alert-danger {
    padding: 30px;
    font-size: 12px
}

.alert-success {
    padding: 30px;
    font-size: 12px;
    background: #d02333
}

.edit-profile__avatar {
    float: left;
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.edit-profile__avatar-img {
    width: 100px;
    height: 100px;
    overflow: hidden;
    border-radius: 50%
}

.edit-profile__avatar-img img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover
}

.edit-profile__avatar-info {
    padding: 30px;
    color: #d02333;
    font-size:   15px
}

.edit-profile__avatar-info #upload {
    display: none
}

.edit-profile__avatar-info a {
    color: #d02333;
    font-size: 14px;
    float: left;
    padding-bottom: 2px;
    border-bottom: 1px dashed #d02333
}

.edit-profile__avatar-info figcaption {
    width: 100%;
    float: left;
    color: gray;
    padding-top: 10px
}

.edit-profile__content {
    width: 100%;
    float: left;
    border-radius: 5px
}

.edit-profile__content .login {
    padding: 0;
    margin: 15px 0 0
}

.edit-profile__content h1 {
    font-size: 16px;
    float: left;
    width: 100%;
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.edit-profile__content h1:after {
    content: "";
    width: 70%;
    height: 15px;
    background-color: #fff;
    background-image: url("data:image/svg+xml,%3Csvg width='6' height='6' viewBox='0 0 6 6' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23dedede' fill-opacity='0.9' fill-rule='evenodd'%3E%3Cpath d='M5 0h1L0 6V5zM6 5v1H5z'/%3E%3C/g%3E%3C/svg%3E");
    position: absolute;
    right: 0
}

.edit-profile .inside-div {
    float: left;
    width: 100%;
    margin-top: 15px
}

.alert-danger {
    border-color: #d02333;
    background: #d02333
}

.edit-btn {
    max-width: 220px
}

.cart .section-title h2 {
    font-size: 25px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.cart .section-title h2 a {
    color: #d02333;
    font-size: 14px
}

.cart .one-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.cart-item {
    padding: 10px 0;
    padding-right: 10px;
}

.cart-item__top {
    margin-bottom: 20px
}

.cart-item__top ul li {
    float: left;
    margin-right: 30px
}

.cart-item__top ul li a {
    color: #d02333
}

.cart-item__main {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background: #fff;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 10px 15px 30px rgba(1,0,0,.1);
}

.cart-item__main .check {
    width: 50px
}

.cart-item__main .name {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.cart-item__main .name figure {
    width: 80px;
    height: 80px;
    overflow: hidden;
    float: left;
    margin-right: 20px;
    border-radius: 10px;
}

.cart-item__main .name figure img {
    -o-object-fit: cover;
    object-fit: cover;
}

.cart-item__main .name h4 {
    font-size: 14px;
    color: #2f4858
}

.cart-item__main .name span {
    font-size: 12px;
    color: #9f9f9f;
    width: 100%;
    float: left;
    margin-top: 10px
}

.cart-item .qty .number {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.cart-item .qty span {
    width: 45px;
    height: 45px;
    cursor: pointer
}

.cart-item .qty span:nth-child(1) {
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg)
}

.cart-item .qty input {
    width: 45px;
    height: 45px;
    background: #f3f3f3;
    border-radius: 10px;
    text-align: center
}

.cart-item .price {
    font-size: 16px;
    color: #d02333
}

.cart-item .act a {
    margin-left: 20px
}

.cart-item .act a svg {
    fill: #9f9f9f
}

.all h1 {
    font-size: 18px;
    margin-bottom: 20px
}

.all ul li {
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    align-itemss: center;
    margin-bottom: 20px;
    background:#f2f2f2;
    padding:10px 15px;
    border-radius: 5px;
}

.all ul li:last-child {
    margin: 0
}

.all ul li:nth-child(3) {
    border-bottom: 1px solid #f3f3f3;
    padding-bottom: 20px
}

.all ul li:nth-child(4) p {
    color: #d02333;
    font-size: 16px
}

.promo {
    margin-top: 40px
}

.promo h1 {
    font-size: 18px
}

.promo .have {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: space-betweens;
    -ms-flex-pack: space-betweens;
    justify-content: space-betweens
}

.promo .have h2 {
    font-size: 14px;
    width: calc(100% - 50px)
}

.promo .have label {
    display: block;
    position: relative
}

.promo .have label > input[type=checkbox] {
    display: none
}

.promo .have .check-1 {
    width: 50px;
    height: 30px;
    border-radius: 50px;
    position: relative;
    float: right
}

.promo .have .check-1 .inner {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50px;
    border: 2px solid #e8e8e8;
    background: #f9f9f9;
    -webkit-transition: all .2s ease;
    -o-transition: all .2s ease;
    transition: all .2s ease
}

.promo .have .check-1 .bullet {
    position: relative;
    width: 25px;
    height: 25px;
    background: #eee;
    border-radius: 50%;
    -webkit-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease;
    top: 2px;
    left: 2px;
    -webkit-box-shadow: 0 3px 3px rgba(0, 0, 0, .15);
    box-shadow: 0 3px 3px rgba(0, 0, 0, .15)
}

.promo .have .check-1 input:checked ~ .inner {
    border: 15px solid #d02333;
    -webkit-transition: all .2s ease;
    -o-transition: all .2s ease;
    transition: all .2s ease
}

.promo .have .check-1 input:checked ~ .bullet {
    left: 23px;
    -webkit-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease;
    -webkit-animation: .2s bullet;
    animation: .2s bullet
}

@-webkit-keyframes bullet {
    0%, 100% {
        width: 25px
    }
    40% {
        width: 30px
    }
}

@keyframes bullet {
    0%, 100% {
        width: 25px
    }
    40% {
        width: 30px
    }
}

.promo form {
    position: relative;
    width: 100%;
    float: left;
    border: 1px solid #f3f3f3;
    height: 46px;
    border-radius: 10px;
    overflow: hidden;
    margin: 20px 0
}

.promo form button {
    position: absolute;
    right: 0;
    right: -6px;
    top: -2px;
    cursor: pointer
}

.promo form input {
    width: 100%;
    height: 46px;
    padding: 0 30px
}

.ch {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    border-radius: 15px;
    min-height: 54px
}

.ch svg {
    position: absolute;
    right: 35px
}

.profile {
    padding-top: 70px
}

.profile-sidebar__img {
    width: 100%;
    margin-bottom: 40px
}

.profile-sidebar__img .p-img2 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    height: auto;
    width: 100%
}

.profile-sidebar__img .p-img2__item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.profile-sidebar__img .p-img2__item figure {
    width: 60px;
    height: 60px;
    overflow: hidden;
    border-radius: 50%;
    float: left;
    margin-right: 20px
}

.profile-sidebar__img .p-img2__item h2 {
    font-size: 14px;
    margin-bottom: 5px
}

.profile-sidebar__img .p-img2__item span {
    font-size:   14px;
    color: #9f9f9f
}

.profile-sidebar__menu ul li {
    margin-bottom: 20px
}

.profile-sidebar__menu ul li a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #2f4858;
    position: relative
}

.profile-sidebar__menu ul li a span {
    width: 7px;
    height: 7px;
    border-radius: 50%;
    background: #d02333;
    position: absolute;
    right: 0
}

.profile-sidebar__menu ul li a svg {
    margin-right: 17px
}

.profile-sidebar__menu ul li a svg path {
    stroke: #2f4858
}

.profile-sidebar__menu ul li.active a {
    color: #d02333
}

.profile-sidebar__menu ul li.active a svg path {
    stroke: #d02333
}

.profile-edit {
    padding-left: 50px
}

.profile-edit .form-group {
    margin-bottom: 20px
}

.profile-edit .form-group label {
    font-size:   14px;
    color: #2f4858;
    margin-bottom: 15px
}

.profile-edit .form-group input {
    width: 100%;
    height: 46px;
    border: 1px solid #d7d7d7;
    border-radius: 10px;
    padding: 0 15px
}

.profile .change-password {
    margin-top: 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.profile .change-password a {
    color: #d02333
}

.authors-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    text-align: center;
    border: 1px solid #dedede;
    margin-top: 40px;
    border-radius: 15px;
    padding: 10px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.authors-item__img {
    width: 100%;
    height: 250px;
    border-radius: 15px;
    overflow: hidden;
    border-bottom: 5px solid #d02333;
    position: relative;
}

.color-red {
    color:#d02333;
}

.authors-item__img .badge-icon {
    position: absolute;
    bottom:10px;
    left:10px;
    background:#fff;
    padding:10px;
    border-radius: 15px;
    width:100px;
}
.authors-item__img .badge-icon img {
    height: 30px;
    width:100%;
    object-fit: contain;
}

.authors-item__img figure {
    height: 100%;
    border-radius: 15px;
    overflow: hidden
}

.authors-item__title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-top: 30px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    text-align: center;
    width: 100%;
    align-items: flex-start;
    padding-left:15px;
}

.authors-item__title h3 {
    margin-bottom:10px;
    font-size: 21px;
}

.authors-item__title h4 {
    margin-bottom: 15px;
}

.authors-item__title h4 a {
    color: #d02333;
}

.authors-item__title .whatsapp {
    background:#25D366;
    border-radius: 5px;
    padding:10px 15px;
    color:#fff;
}
.authors-item__title .whatsapp a {
    color:#fff;
    display: flex;
    align-items: center;
}
.authors-item__title .whatsapp svg {
    width: 21px;
    height: 21px;
    margin-right: 10px;
}

.authors-item__title .whatsapp svg path {
    fill:#fff;

}

.authors-item__title ul {
    width: 100%;
    float:left;
    display: flex;
    justify-content: center;
    margin-top: 20px;
    margin-bottom: 15px;
    width: 40px;
    height: 250px;
    background: #d02333;
    position: absolute;
    top: 10px;
    right: -41px;
    transition: 0.3s all ease-in;
    flex-direction: column;
}

.authors-item:hover .authors-item__title ul {
    right:10px;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
}

.authors-item__title ul li {
    float:left;
    margin-right: 10px;
}

.authors-item__title ul li:last-child {
    margin-right: 0;
}

.authors-item__title ul li a {
    width: 40px;
    height: 40px;
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 18px;
    border-radius: 10px;
}



.us-item__title .whatsapp {
    background:#25D366;
    border-radius: 5px;
    padding:10px 15px;
    color:#fff;
}
.us-item__title .whatsapp a {
    color:#fff;
    display: flex;
    align-items: center;
}
.us-item__title .whatsapp svg {
    width: 21px;
    height: 21px;
    margin-right: 10px;
}

.us-item__title .whatsapp svg path {
    fill:#fff;

}

.us-item__title ul {
    width: 100%;
    float:left;
    display: flex;
    justify-content: center;
    margin-top: 20px;
    margin-bottom: 15px;
}

.us-item__title ul li {
    float:left;
    margin-right: 10px;
}

.us-item__title ul li:last-child {
    margin-right: 0;
}

.us-item__title ul li a {
    width: 40px;
    height: 40px;
    float: left;
    border: 1px solid #f2f2f2;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 18px;
    border-radius: 10px;
}



.conff .l-item__img {
    width: 50%
}

.checkbox li {
    margin-bottom: 30px
}

.checkbox li .custom-control {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.checkbox li a {
    color: #242424
}

.checkbox li a:hover {
    color: #d02333
}

.news-item {
    margin-top: 40px
}

.news-item figure {
    height: 240px;
    overflow: hidden;
    border-radius: 15px;
    margin-bottom: 20px
}

.news-item__title h2 {
    margin-bottom: 10px
}

.news-item__title span {
    color: #d02333
}

.news-item a {
    color: #242424
}

.hd-2 {
    margin-top: 100px
}

.hd-2:after {
    display: none
}

.hd-2 .about-header__img {
    width: 100%
}

.hd-3 .about-header__title {
    padding-right: 0;
    padding-left: 50px
}

.video {
    position: relative;
    height: calc(100vh - 180px);
    overflow: hidden;
    background: #242424;
}

.video:Before {
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    background-image: linear-gradient(to right, #2f4858 , transparent);
    left: 0;
    z-index: 300;
}

.video video {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    opacity: .3
}

.video h1 {

    color: #fff;
    z-index: 300;
    font-size: 34px;
    margin-bottom:30px;
}

.header-full {
    position: absolute;
    top: 0;
    z-index: 500
}

.header-full .header-menu__actions a svg path {
    stroke: #fff
}

.header-full .header-menu__actions a:last-child {
    margin-left: 30px
}

.header-full .header-menu__actions a:last-child svg path {
    stroke: none;
    fill: #fff
}

.testimonials-item {
    border-radius: 15px
}

.testimonials-item__img {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-left: 20px
}

.testimonials-item__img figure {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #dedede;
    margin-right: 20px;
    padding: 5px
}

.testimonials-item__img figure img {
    border-radius: 50%
}

.testimonials-item__img .title-it span {
    font-size: 16px;

    margin-bottom: 5px;
    color: #d02333;
    float: lefts
}

.testimonials-item__img .title-it p {
    float: left;
    color: #888;

}

.testimonials-item__desc {
    margin-top: 30px;
    padding: 30px;
    background: #dedede;
    color: #fff;
    border-radius: 15px;
    border-bottom-right-radius: 55px;
    margin-bottom: 30px;
    position: relative
}

.testimonials-item__desc:after {
    content: "";
    width: 40px;
    height: 20px;
    border-top: solid 30px #dedede;
    border-left: solid 30px transparent;
    border-right: solid 30px transparent;
    position: absolute;
    bottom: -15px;
    left: 20px
}

.testimonials-item__desc h2 {
    font-family:"p_regular";
    font-weight:normal;
    margin-bottom: 10px;
    color: #242424
}

.testimonials-item__img .title-it span {
    font-family:"p_regular"!important;
    font-weight:normal!important;;
}

.testimonials-item__desc p {
    line-height: 1.7;
    color: #242424;
    opacity: .7
}

.faq-list {
    width: 100%;
    float: left
}

.faq-list__item {
    background: #f2f2f2;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 12px;
    padding: 19px 30px;
    margin-bottom: 32px
}

.faq-list__item--title h3 a {
    color: #2f4858;
    font-size: 16px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.faq-list__item--title h3 a:after {
    content: "";
    width: 30px;
    height: 30px;
    float: right;
    border-radius: 50%;
    background-image: url(../img/Plus.svg);
    background-position: center;
    background-repeat: no-repeat
}

.faq-list__item--text {
    margin-top: 24px;
    float: left;
    display: none
}

.faq-list__item--text p {
    font-size: 14px;
    line-height: 1.74px;
    color: #8c96aa
}

.faq-list__item.active > .faq-list__item--title h3 a:after {
    background-image: url(../img/Minus.svg)
}

.faq-list__item.active > .faq-list__item--text {
    display: block
}

.swiper-testi {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.testimonials-slider {
    padding-bottom: 40px;
    width: 100%;
}

.testimonials-item {
    width: 100%;
    float: left;
}

.testimonials-img {
    width: calc(100% + 100px);
    height: 450px;
    float: left;
    position: relative;
    margin-left:-100px;
}

.testimonials-item__img .title-it {
    width: calc(100% - 80px);
}

.testimonials-img figure {
    height: 100%;
    border-radius: 20px;
    overflow: hidden;
    position: relative;
    background:#242424;
}

.testimonials-img figure:after {
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    left:0;
    top:0;
    background-image: linear-gradient(to right, #d02333 , transparent);
    opacity: 0.9;

}


.testimonials-img figure img {
    opacity: 0.5;
}


.courses {
    position: relative
}

.courses:before {
    width: 100%;
    height: 350px;
    background-color: #2f4858;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='199' viewBox='0 0 100 199'%3E%3Cg fill='%23213644' fill-opacity='0.4'%3E%3Cpath d='M0 199V0h1v1.99L100 199h-1.12L1 4.22V199H0zM100 2h-.12l-1-2H100v2z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E");
    content: "";
    position: absolute;
    top: 0
}

.courses-slider {
    margin-bottom: 60px
}

.courses .section-title ul {
    margin-top: 10px;
    position: absolute;
    right: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.courses .section-title ul li {
    cursor: pointer;
    font-size: 34px;
    color: #d02333
}

.courses .section-title ul li.swiper-button-disabled {
    color: #fff
}

.spead {
    margin: 15px 0;
    margin-bottom: 0
}

.spead ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.spead ul li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-size:12px;
}

.spead ul li:after {
    content: "/";
    float: right;
    margin: 0 10px
}

.spead ul li a {
    float: left;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #2f4858;
    font-size:12px;
}

.spead ul li a svg {
    width: 23px;
    height: 23px;
    margin-right: 10px
}

.spead ul li a svg path {
    stroke: #d02333
}

.spead ul li.active {
    color: #d02333
}

.spead ul li:last-child:after {
    display: none
}

.load-more {
    border: 2px solid #d02333;
    color: #d02333;
    margin-top: 30px
}

.cover {
    height: 350px
}

.cover .bg-img {
    opacity: .5
}

.cover .spead {
    height: 350px;
    z-index: 300;
    margin-top: 0;
    padding-top: 30px;
    position: relative
}

.cover .spead ul li {
    color: #fff
}

.cover .spead ul li.active {
    color: #fff
}

.cover .spead ul li a {
    color: #fff
}

.cover .spead ul li a svg path {
    stroke: #fff
}

.cover .spead h1 {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-size: 42px;
    color: #fff;
    letter-spacing: 2px;
    max-width: 80%;
    line-height: 1.5;
}

.cover {
    position: relative;
}

.cover:before {
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    background-image: linear-gradient(to right, #2f4858 , transparent);
    left: 0;
    z-index: 300;
}

@-webkit-keyframes rotating {
    from {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
    to {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }
}

@keyframes rotating {
    from {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
    to {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }
}

.cover .rotating {
    -webkit-animation: rotating 12s linear infinite;
    animation: rotating 12s linear infinite
}

@-webkit-keyframes coloring {
    0% {
        fill: #fff
    }
    35% {
        fill: #fff
    }
    75% {
        fill: gray
    }
    100% {
        fill: #fff
    }
}

@keyframes coloring {
    0% {
        fill: #fff
    }
    35% {
        fill: #fff
    }
    75% {
        fill: gray
    }
    100% {
        fill: #fff
    }
}

.cover .coloring {
    -webkit-animation: coloring 12s linear infinite;
    animation: coloring 12s linear infinite
}

.cover .scroll-down {
    position: absolute;
    bottom: 30px;
    right: 0;
    z-index: 400;
    display: none;
}

.cover .scroll-down .rounded-text {
    width: 150px;
    height: 150px;

    -webkit-transform: rotate(-103deg);
    -ms-transform: rotate(-103deg);
    transform: rotate(-103deg);
    color: #fff;
    font-weight: 700;
    z-index: -1;
    font-family: Raleway
}

.choose-c {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-top: 20px
}

.choose-c__item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.choose-c__item h2 {
    font-size:   15px
}

.choose-c__item ul li {
    float: left;
    margin-right: 15px
}

.choose-c__item ul li a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #2f4858;

    font-size: 8pt
}

.choose-c__item ul li.active a {
    color: #d02333
}

.choose-c__item ul li figure {
    width: 25px;
    height: 25px;
    overflow: hidden;
    border-radius: 50%;
    float: left;
    margin-right: 10px;
    border: 1px solid #d02333;
    padding: 2px
}

.choose-c__item ul li figure img {
    border-radius: 50%
}

.choose {
    margin-top: 30px
}

.choose-item {
    background: #ececec;
    border-radius: 15px;
    padding: 15px;
    border: 2px solid transparent;
    cursor: pointer
}

.choose-item.active {
    border: 2px solid #2f4858
}

.choose-item .country {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-bottom: 1px solid #fff;
    padding-bottom: 10px
}

.choose-item .country img {
    width: 25px;
    height: 25px;
    margin-right: 10px;
    border-radius: 50%;
    overflow: hidden;
    object-fit: cover;
}

.choose-item .country h2 {

}

.choose-item .date {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    margin-top: 10px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.choose-item .date a {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end
}

.choose-item .date a svg {
    width: 24px;
    height: 24px
}

.choose-item .date svg {
    width: 24px;
    height: 24px;
    margin-right: 10px;
}

.choose-item .date span , .choose-item .date strong {
    display: flex;
    align-items: center;
}

.choose-item .date a svg path {
    stroke: #d02333
}

.choose-item .date p {
    margin-top: 10px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 100%;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.choose-item .date p a {
    color: #d02333;
    border:1px solid #d02333;
    padding:5px 10px;
    border-radius: 10px;
    transition: 0.3s all ease-in;
}
.choose-item .date p a:hover {
    background:  #d02333;
    color:#fff;
}

.choose-item .date p a:hover svg path {
    stroke:#fff;
}
.why_us-item {
    padding: 30px;
    background: #fff;
    border-radius: 15px;
    position: relative;
    -webkit-box-shadow: 10px 15px 30px rgba(1, 0, 0, .1);
    box-shadow: 10px 15px 30px rgba(1, 0, 0, .1);
    margin-top:50px;
}

.why_us-item:before {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #fff;
    opacity: .1;
    position: absolute;
    top: 30px;
    left: 30px;
    content: ""
}

.why_us-item .title-in {
    margin-top: 5px
}

.why_us-item .title-in h3 {
    font-size: 18px;
    color: #242424;
    font-weight: 700;
    font-family: "p_regular";

}

.why_us-item .title-in p {
    line-height: 1.7;
    color: gray;
    margin-top: 15px
}


.faq .section-title {
    margin-bottom:80px;
}

.partners-full {
    margin-top:110px!important;
}

.related .section-title {
    margin-bottom: 50px;
}

.faq {
    background: #f2f2f2;
    padding: 80px 0;
    margin-top: 80px;

}

.faq .collapsible-link {
    width: 100%;
    position: relative;
    text-align: left;
    display: flex;
    align-items: center;
    padding:0;
    border-radius:0;
    padding-bottom: 10px;
    border-bottom:2px solid #2f4858;
}



.faq .collapsible-link::before {
    content: "\eab2";
    position: absolute;
    top: 0;
    right: 0;
    display: block;
    font-family: Icofont;
    font-size: 1.1rem
}

.faq .collapsible-link[aria-expanded=true]::before {
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg)
}


.faq .card-body {
    padding: 15px 20px !important
}

.faq .card-body p {
    line-height: 1.7;
    color: Gray
}

.faq .card {
    margin-bottom: 10px;
    border-radius: 15px;
    overflow: hidden;
    background:transparent;
    border: none
}

.card .bg-white {
    background: transparent!important;
}

.faq .collapsible-link {
    text-transform: unset!important;
}

.faq figure {
    width: 100%
}

.faq figure svg {
    width: 100%;
    height: 340px
}

@media (min-width: 1660px) {
    .container {
        max-width: 1440px
    }
}

.au {
    display: flex;
    align-items: center;
}

.au figure {
    width: 50px;
    height: 50px;
    overflow: hidden;
    border-radius: 50%;
    padding: 2px;
    border: 2px solid #e11d25;
    margin-right: 15px;
}

.au figure img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.au {
    margin-right: 10px;
    color: #242424;
}

.au:hover {
    color: #e11d25;
}

.loginned {
    margin-right: 0;
}

.swiper-pagination1 {
    width:100%;
    float:left;
    margin-top:60px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.swiper-pagination1 .swiper-pagination-bullet {
    width: 7px;
    height: 7px;
    background:#2f4858;
    border-radius: 50%;
    opacity: 1;
    margin-right: 15px;
}

.swiper-pagination1 .swiper-pagination-bullet-active {
    width: 40px;
    height: 7px;
    border-radius: 5px;
    background:#d02333;
}

.testimonials-icon svg {
    width:80px;
    height: 80px;
    fill:#d02333;
}

.marquee-wall {
    padding-bottom: 80px;
}

.marquee {
    position: relative;
}

.marquee > * {
    font-size: 150px;
    font-weight: 500;
    line-height: 1.4;
    letter-spacing: 2.5px;
    white-space: nowrap;

}

.js-first {
    color:#dedede;
}

.js-second {
    color:#d02333
}

@media (max-width: 991px) {
    .marquee > * {
        font-size: 90px;
        line-height: 1.3;
    }
}

@media (max-width: 767px) {
    .marquee > * {
        font-size: 80px;
    }
}

.marquee > *:nth-child(1) {
    position: relative;
    left: 250px;
}

@media (max-width: 767px) {
    .marquee > *:nth-child(1) {
        left: 20%;
    }
}

.marquee > *:nth-child(2) {
    position: relative;
    right: 70%;
}

@media (max-width: 767px) {
    .marquee > *:nth-child(2) {
        right: 70%;
    }
}

.preloader {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 5000;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    background-color: white;
    transition: opacity 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.preloader__wrap {
    overflow: hidden;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 72px;
    height: 72px;
    border-radius: 30px;
    background-color: white;
    box-shadow: 0px 2px 24px 0px #00000014;
    animation-name: preloaderWraper;
    animation-iteration-count: infinite;
    animation-duration: 1.8s;
    animation-timing-function: ease;
}

.preloader__icon {
    position: absolute;
    animation-name: preloader;
    animation-iteration-count: infinite;
    animation-duration: 1.8s;
    animation-timing-function: ease;
}

.preloader__title {
    font-size: 42px;

    font-weight: 500;
    line-height: 1.2;
    letter-spacing: 0.04em;
    margin-top: 20px;
}

.preloader.-is-hidden {
    opacity: 0;
    pointer-events: none;
}

@keyframes preloaderWraper {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: none;
    }
}

@keyframes preloader {
    0% {
        transform: translateY(150%) scale(1);
    }
    50% {
        transform: translateY(0%) scale(1.2) rotate(20deg);
    }
    100% {
        transform: translateY(-150%) rotate(-20deg);
    }
}

.login-icon svg {
    width:100%;
    height: 100%;
}

#stats {
    margin-top: 80px;
}

.choose-item {
    cursor: pointer;
}

.modal-backdrop {
    background: rgba(1, 0, 0, 0.7);
    backdrop-filter: blur(8px);
    opacity: 1 !important;
}
.a-title {
    margin-bottom: 30px;
}
.a-title h3 {
    font-family: p_bold, sans-serif;
    font-weight: 700;
    line-height: 130%;
    color: #d02333;

    margin-bottom: 10px;
}

.a-title p {
    color:#888888;
    line-height: 1.7;
}

.btn-primary {
    padding:12px 30px;
    border-radius: 10px;
    background: #d02333;
    border-color: #d02333;;
}


.btn-primary:hover {
    background: #2f4858;
    border-color:#2f4858;
}

.modal-content {
    padding:15px;
    border-radius: 10px;
}

.modal-header {

    font-family: p_bold, sans-serif;
    font-weight: 700;
    border:none;
}

.modal-footer {
    border:none;
    padding-top: 20px;
}

.largemodal .modal-dialog {
    max-width: 700px;
}
.iti {
    position: relative;
    display: inline-block; }
.iti * {
    box-sizing: border-box;
    -moz-box-sizing: border-box; }
.iti__hide {
    display: none; }
.iti__v-hide {
    visibility: hidden; }
.iti input, .iti input[type=text], .iti input[type=tel] {
    position: relative;
    z-index: 0;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    padding-right: 36px;
    margin-right: 0; }
.iti__flag-container {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    padding: 1px; }
.iti__selected-flag {
    z-index: 1;
    position: relative;
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 6px 0 8px; }
.iti__arrow {
    margin-left: 6px;
    width: 0;
    height: 0;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-top: 4px solid #555; }
.iti__arrow--up {
    border-top: none;
    border-bottom: 4px solid #555; }
.iti__country-list {
    position: absolute;
    z-index: 2;
    list-style: none;
    text-align: left;
    padding: 0;
    margin: 0 0 0 -1px;
    box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.2);
    background-color: white;
    border: 1px solid #CCC;
    white-space: nowrap;
    max-height: 200px;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch; }
.iti__country-list--dropup {
    bottom: 100%;
    margin-bottom: -1px; }
@media (max-width: 500px) {
    .iti__country-list {
        white-space: normal; } }
.iti__flag-box {
    display: inline-block;
    width: 20px; }
.iti__divider {
    padding-bottom: 5px;
    margin-bottom: 5px;
    border-bottom: 1px solid #CCC; }
.iti__country {
    padding: 5px 10px;
    outline: none; }
.iti__dial-code {
    color: #999; }
.iti__country.iti__highlight {
    background-color: rgba(0, 0, 0, 0.05); }
.iti__flag-box, .iti__country-name, .iti__dial-code {
    vertical-align: middle; }
.iti__flag-box, .iti__country-name {
    margin-right: 6px; }
.iti--allow-dropdown input, .iti--allow-dropdown input[type=text], .iti--allow-dropdown input[type=tel], .iti--separate-dial-code input, .iti--separate-dial-code input[type=text], .iti--separate-dial-code input[type=tel] {
    padding-right: 6px;
    padding-left: 52px;
    margin-left: 0; }
.iti--allow-dropdown .iti__flag-container, .iti--separate-dial-code .iti__flag-container {
    right: auto;
    left: 0; }
.iti--allow-dropdown .iti__flag-container:hover {
    cursor: pointer; }
.iti--allow-dropdown .iti__flag-container:hover .iti__selected-flag {
    background-color: rgba(0, 0, 0, 0.05); }
.iti--allow-dropdown input[disabled] + .iti__flag-container:hover,
.iti--allow-dropdown input[readonly] + .iti__flag-container:hover {
    cursor: default; }
.iti--allow-dropdown input[disabled] + .iti__flag-container:hover .iti__selected-flag,
.iti--allow-dropdown input[readonly] + .iti__flag-container:hover .iti__selected-flag {
    background-color: transparent; }
.iti--separate-dial-code .iti__selected-flag {
    background-color: rgba(0, 0, 0, 0.05); }
.iti--separate-dial-code .iti__selected-dial-code {
    margin-left: 6px; }
.iti--container {
    position: absolute;
    top: -1000px;
    left: -1000px;
    z-index: 1060;
    padding: 1px; }
.iti--container:hover {
    cursor: pointer; }

.iti-mobile .iti--container {
    top: 30px;
    bottom: 30px;
    left: 30px;
    right: 30px;
    position: fixed; }

.iti-mobile .iti__country-list {
    max-height: 100%;
    width: 100%; }

.iti-mobile .iti__country {
    padding: 10px 10px;
    line-height: 1.7em; }

.iti__flag {
    width: 20px; }
.iti__flag.iti__be {
    width: 18px; }
.iti__flag.iti__ch {
    width: 15px; }
.iti__flag.iti__mc {
    width: 19px; }
.iti__flag.iti__ne {
    width: 18px; }
.iti__flag.iti__np {
    width:   15px; }
.iti__flag.iti__va {
    width: 15px; }
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .iti__flag {
        background-size: 5652px 15px; } }
.iti__flag.iti__ac {
    height: 10px;
    background-position: 0px 0px; }
.iti__flag.iti__ad {
    height: 14px;
    background-position: -22px 0px; }
.iti__flag.iti__ae {
    height: 10px;
    background-position: -44px 0px; }
.iti__flag.iti__af {
    height: 14px;
    background-position: -66px 0px; }
.iti__flag.iti__ag {
    height: 14px;
    background-position: -88px 0px; }
.iti__flag.iti__ai {
    height: 10px;
    background-position: -110px 0px; }
.iti__flag.iti__al {
    height: 15px;
    background-position: -132px 0px; }
.iti__flag.iti__am {
    height: 10px;
    background-position: -154px 0px; }
.iti__flag.iti__ao {
    height: 14px;
    background-position: -176px 0px; }
.iti__flag.iti__aq {
    height: 14px;
    background-position: -198px 0px; }
.iti__flag.iti__ar {
    height:   15px;
    background-position: -220px 0px; }
.iti__flag.iti__as {
    height: 10px;
    background-position: -242px 0px; }
.iti__flag.iti__at {
    height: 14px;
    background-position: -264px 0px; }
.iti__flag.iti__au {
    height: 10px;
    background-position: -286px 0px; }
.iti__flag.iti__aw {
    height: 14px;
    background-position: -308px 0px; }
.iti__flag.iti__ax {
    height:   15px;
    background-position: -330px 0px; }
.iti__flag.iti__az {
    height: 10px;
    background-position: -352px 0px; }
.iti__flag.iti__ba {
    height: 10px;
    background-position: -374px 0px; }
.iti__flag.iti__bb {
    height: 14px;
    background-position: -396px 0px; }
.iti__flag.iti__bd {
    height: 12px;
    background-position: -418px 0px; }
.iti__flag.iti__be {
    height: 15px;
    background-position: -440px 0px; }
.iti__flag.iti__bf {
    height: 14px;
    background-position: -460px 0px; }
.iti__flag.iti__bg {
    height: 12px;
    background-position: -482px 0px; }
.iti__flag.iti__bh {
    height: 12px;
    background-position: -504px 0px; }
.iti__flag.iti__bi {
    height: 12px;
    background-position: -526px 0px; }
.iti__flag.iti__bj {
    height: 14px;
    background-position: -548px 0px; }
.iti__flag.iti__bl {
    height: 14px;
    background-position: -570px 0px; }
.iti__flag.iti__bm {
    height: 10px;
    background-position: -592px 0px; }
.iti__flag.iti__bn {
    height: 10px;
    background-position: -614px 0px; }
.iti__flag.iti__bo {
    height: 14px;
    background-position: -636px 0px; }
.iti__flag.iti__bq {
    height: 14px;
    background-position: -658px 0px; }
.iti__flag.iti__br {
    height: 14px;
    background-position: -680px 0px; }
.iti__flag.iti__bs {
    height: 10px;
    background-position: -702px 0px; }
.iti__flag.iti__bt {
    height: 14px;
    background-position: -724px 0px; }
.iti__flag.iti__bv {
    height: 15px;
    background-position: -746px 0px; }
.iti__flag.iti__bw {
    height: 14px;
    background-position: -768px 0px; }
.iti__flag.iti__by {
    height: 10px;
    background-position: -790px 0px; }
.iti__flag.iti__bz {
    height: 14px;
    background-position: -812px 0px; }
.iti__flag.iti__ca {
    height: 10px;
    background-position: -834px 0px; }
.iti__flag.iti__cc {
    height: 10px;
    background-position: -856px 0px; }
.iti__flag.iti__cd {
    height: 15px;
    background-position: -878px 0px; }
.iti__flag.iti__cf {
    height: 14px;
    background-position: -900px 0px; }
.iti__flag.iti__cg {
    height: 14px;
    background-position: -922px 0px; }
.iti__flag.iti__ch {
    height: 15px;
    background-position: -944px 0px; }
.iti__flag.iti__ci {
    height: 14px;
    background-position: -961px 0px; }
.iti__flag.iti__ck {
    height: 10px;
    background-position: -983px 0px; }
.iti__flag.iti__cl {
    height: 14px;
    background-position: -1005px 0px; }
.iti__flag.iti__cm {
    height: 14px;
    background-position: -1027px 0px; }
.iti__flag.iti__cn {
    height: 14px;
    background-position: -1049px 0px; }
.iti__flag.iti__co {
    height: 14px;
    background-position: -1071px 0px; }
.iti__flag.iti__cp {
    height: 14px;
    background-position: -1093px 0px; }
.iti__flag.iti__cr {
    height: 12px;
    background-position: -1115px 0px; }
.iti__flag.iti__cu {
    height: 10px;
    background-position: -1137px 0px; }
.iti__flag.iti__cv {
    height: 12px;
    background-position: -1159px 0px; }
.iti__flag.iti__cw {
    height: 14px;
    background-position: -1181px 0px; }
.iti__flag.iti__cx {
    height: 10px;
    background-position: -1203px 0px; }
.iti__flag.iti__cy {
    height: 14px;
    background-position: -1225px 0px; }
.iti__flag.iti__cz {
    height: 14px;
    background-position: -1247px 0px; }
.iti__flag.iti__de {
    height: 12px;
    background-position: -1269px 0px; }
.iti__flag.iti__dg {
    height: 10px;
    background-position: -1291px 0px; }
.iti__flag.iti__dj {
    height: 14px;
    background-position: -13  15px 0px; }
.iti__flag.iti__dk {
    height: 15px;
    background-position: -1335px 0px; }
.iti__flag.iti__dm {
    height: 10px;
    background-position: -1357px 0px; }
.iti__flag.iti__do {
    height: 14px;
    background-position: -1379px 0px; }
.iti__flag.iti__dz {
    height: 14px;
    background-position: -1401px 0px; }
.iti__flag.iti__ea {
    height: 14px;
    background-position: -1423px 0px; }
.iti__flag.iti__ec {
    height: 14px;
    background-position: -1445px 0px; }
.iti__flag.iti__ee {
    height:   15px;
    background-position: -1467px 0px; }
.iti__flag.iti__eg {
    height: 14px;
    background-position: -1489px 0px; }
.iti__flag.iti__eh {
    height: 10px;
    background-position: -1511px 0px; }
.iti__flag.iti__er {
    height: 10px;
    background-position: -1533px 0px; }
.iti__flag.iti__es {
    height: 14px;
    background-position: -1555px 0px; }
.iti__flag.iti__et {
    height: 10px;
    background-position: -1577px 0px; }
.iti__flag.iti__eu {
    height: 14px;
    background-position: -1599px 0px; }
.iti__flag.iti__fi {
    height: 12px;
    background-position: -1621px 0px; }
.iti__flag.iti__fj {
    height: 10px;
    background-position: -1643px 0px; }
.iti__flag.iti__fk {
    height: 10px;
    background-position: -1665px 0px; }
.iti__flag.iti__fm {
    height: 11px;
    background-position: -1687px 0px; }
.iti__flag.iti__fo {
    height: 15px;
    background-position: -1709px 0px; }
.iti__flag.iti__fr {
    height: 14px;
    background-position: -1731px 0px; }
.iti__flag.iti__ga {
    height: 15px;
    background-position: -1753px 0px; }
.iti__flag.iti__gb {
    height: 10px;
    background-position: -1775px 0px; }
.iti__flag.iti__gd {
    height: 12px;
    background-position: -1797px 0px; }
.iti__flag.iti__ge {
    height: 14px;
    background-position: -1819px 0px; }
.iti__flag.iti__gf {
    height: 14px;
    background-position: -1841px 0px; }
.iti__flag.iti__gg {
    height: 14px;
    background-position: -1863px 0px; }
.iti__flag.iti__gh {
    height: 14px;
    background-position: -1885px 0px; }
.iti__flag.iti__gi {
    height: 10px;
    background-position: -1907px 0px; }
.iti__flag.iti__gl {
    height: 14px;
    background-position: -1929px 0px; }
.iti__flag.iti__gm {
    height: 14px;
    background-position: -1951px 0px; }
.iti__flag.iti__gn {
    height: 14px;
    background-position: -1973px 0px; }
.iti__flag.iti__gp {
    height: 14px;
    background-position: -1995px 0px; }
.iti__flag.iti__gq {
    height: 14px;
    background-position: -2017px 0px; }
.iti__flag.iti__gr {
    height: 14px;
    background-position: -2039px 0px; }
.iti__flag.iti__gs {
    height: 10px;
    background-position: -2061px 0px; }
.iti__flag.iti__gt {
    height:   15px;
    background-position: -2083px 0px; }
.iti__flag.iti__gu {
    height: 11px;
    background-position: -2105px 0px; }
.iti__flag.iti__gw {
    height: 10px;
    background-position: -2127px 0px; }
.iti__flag.iti__gy {
    height: 12px;
    background-position: -2149px 0px; }
.iti__flag.iti__hk {
    height: 14px;
    background-position: -2171px 0px; }
.iti__flag.iti__hm {
    height: 10px;
    background-position: -2193px 0px; }
.iti__flag.iti__hn {
    height: 10px;
    background-position: -2215px 0px; }
.iti__flag.iti__hr {
    height: 10px;
    background-position: -2237px 0px; }
.iti__flag.iti__ht {
    height: 12px;
    background-position: -2259px 0px; }
.iti__flag.iti__hu {
    height: 10px;
    background-position: -2281px 0px; }
.iti__flag.iti__ic {
    height: 14px;
    background-position: -2303px 0px; }
.iti__flag.iti__id {
    height: 14px;
    background-position: -2325px 0px; }
.iti__flag.iti__ie {
    height: 10px;
    background-position: -2347px 0px; }
.iti__flag.iti__il {
    height: 15px;
    background-position: -2369px 0px; }
.iti__flag.iti__im {
    height: 10px;
    background-position: -2391px 0px; }
.iti__flag.iti__in {
    height: 14px;
    background-position: -24  15px 0px; }
.iti__flag.iti__io {
    height: 10px;
    background-position: -2435px 0px; }
.iti__flag.iti__iq {
    height: 14px;
    background-position: -2457px 0px; }
.iti__flag.iti__ir {
    height: 12px;
    background-position: -2479px 0px; }
.iti__flag.iti__is {
    height: 15px;
    background-position: -2501px 0px; }
.iti__flag.iti__it {
    height: 14px;
    background-position: -2523px 0px; }
.iti__flag.iti__je {
    height: 12px;
    background-position: -2545px 0px; }
.iti__flag.iti__jm {
    height: 10px;
    background-position: -2567px 0px; }
.iti__flag.iti__jo {
    height: 10px;
    background-position: -2589px 0px; }
.iti__flag.iti__jp {
    height: 14px;
    background-position: -2611px 0px; }
.iti__flag.iti__ke {
    height: 14px;
    background-position: -2633px 0px; }
.iti__flag.iti__kg {
    height: 12px;
    background-position: -2655px 0px; }
.iti__flag.iti__kh {
    height:   15px;
    background-position: -2677px 0px; }
.iti__flag.iti__ki {
    height: 10px;
    background-position: -2699px 0px; }
.iti__flag.iti__km {
    height: 12px;
    background-position: -2721px 0px; }
.iti__flag.iti__kn {
    height: 14px;
    background-position: -2743px 0px; }
.iti__flag.iti__kp {
    height: 10px;
    background-position: -2765px 0px; }
.iti__flag.iti__kr {
    height: 14px;
    background-position: -2787px 0px; }
.iti__flag.iti__kw {
    height: 10px;
    background-position: -2809px 0px; }
.iti__flag.iti__ky {
    height: 10px;
    background-position: -2831px 0px; }
.iti__flag.iti__kz {
    height: 10px;
    background-position: -2853px 0px; }
.iti__flag.iti__la {
    height: 14px;
    background-position: -2875px 0px; }
.iti__flag.iti__lb {
    height: 14px;
    background-position: -2897px 0px; }
.iti__flag.iti__lc {
    height: 10px;
    background-position: -2919px 0px; }
.iti__flag.iti__li {
    height: 12px;
    background-position: -2941px 0px; }
.iti__flag.iti__lk {
    height: 10px;
    background-position: -2963px 0px; }
.iti__flag.iti__lr {
    height: 11px;
    background-position: -2985px 0px; }
.iti__flag.iti__ls {
    height: 14px;
    background-position: -3007px 0px; }
.iti__flag.iti__lt {
    height: 12px;
    background-position: -3029px 0px; }
.iti__flag.iti__lu {
    height: 12px;
    background-position: -3051px 0px; }
.iti__flag.iti__lv {
    height: 10px;
    background-position: -3073px 0px; }
.iti__flag.iti__ly {
    height: 10px;
    background-position: -3095px 0px; }
.iti__flag.iti__ma {
    height: 14px;
    background-position: -3117px 0px; }
.iti__flag.iti__mc {
    height: 15px;
    background-position: -3139px 0px; }
.iti__flag.iti__md {
    height: 10px;
    background-position: -3160px 0px; }
.iti__flag.iti__me {
    height: 10px;
    background-position: -3182px 0px; }
.iti__flag.iti__mf {
    height: 14px;
    background-position: -3204px 0px; }
.iti__flag.iti__mg {
    height: 14px;
    background-position: -3226px 0px; }
.iti__flag.iti__mh {
    height: 11px;
    background-position: -3248px 0px; }
.iti__flag.iti__mk {
    height: 10px;
    background-position: -3270px 0px; }
.iti__flag.iti__ml {
    height: 14px;
    background-position: -3292px 0px; }
.iti__flag.iti__mm {
    height: 14px;
    background-position: -3314px 0px; }
.iti__flag.iti__mn {
    height: 10px;
    background-position: -3336px 0px; }
.iti__flag.iti__mo {
    height: 14px;
    background-position: -3358px 0px; }
.iti__flag.iti__mp {
    height: 10px;
    background-position: -3380px 0px; }
.iti__flag.iti__mq {
    height: 14px;
    background-position: -3402px 0px; }
.iti__flag.iti__mr {
    height: 14px;
    background-position: -3424px 0px; }
.iti__flag.iti__ms {
    height: 10px;
    background-position: -3446px 0px; }
.iti__flag.iti__mt {
    height: 14px;
    background-position: -3468px 0px; }
.iti__flag.iti__mu {
    height: 14px;
    background-position: -3490px 0px; }
.iti__flag.iti__mv {
    height: 14px;
    background-position: -3512px 0px; }
.iti__flag.iti__mw {
    height: 14px;
    background-position: -3534px 0px; }
.iti__flag.iti__mx {
    height: 12px;
    background-position: -3556px 0px; }
.iti__flag.iti__my {
    height: 10px;
    background-position: -3578px 0px; }
.iti__flag.iti__mz {
    height: 14px;
    background-position: -3600px 0px; }
.iti__flag.iti__na {
    height: 14px;
    background-position: -3622px 0px; }
.iti__flag.iti__nc {
    height: 10px;
    background-position: -3644px 0px; }
.iti__flag.iti__ne {
    height: 15px;
    background-position: -3666px 0px; }
.iti__flag.iti__nf {
    height: 10px;
    background-position: -3686px 0px; }
.iti__flag.iti__ng {
    height: 10px;
    background-position: -3708px 0px; }
.iti__flag.iti__ni {
    height: 12px;
    background-position: -3730px 0px; }
.iti__flag.iti__nl {
    height: 14px;
    background-position: -3752px 0px; }
.iti__flag.iti__no {
    height: 15px;
    background-position: -3774px 0px; }
.iti__flag.iti__np {
    height: 15px;
    background-position: -3796px 0px; }
.iti__flag.iti__nr {
    height: 10px;
    background-position: -3811px 0px; }
.iti__flag.iti__nu {
    height: 10px;
    background-position: -3833px 0px; }
.iti__flag.iti__nz {
    height: 10px;
    background-position: -3855px 0px; }
.iti__flag.iti__om {
    height: 10px;
    background-position: -3877px 0px; }
.iti__flag.iti__pa {
    height: 14px;
    background-position: -3899px 0px; }
.iti__flag.iti__pe {
    height: 14px;
    background-position: -3921px 0px; }
.iti__flag.iti__pf {
    height: 14px;
    background-position: -3943px 0px; }
.iti__flag.iti__pg {
    height: 15px;
    background-position: -3965px 0px; }
.iti__flag.iti__ph {
    height: 10px;
    background-position: -3987px 0px; }
.iti__flag.iti__pk {
    height: 14px;
    background-position: -4009px 0px; }
.iti__flag.iti__pl {
    height:   15px;
    background-position: -4031px 0px; }
.iti__flag.iti__pm {
    height: 14px;
    background-position: -4053px 0px; }
.iti__flag.iti__pn {
    height: 10px;
    background-position: -4075px 0px; }
.iti__flag.iti__pr {
    height: 14px;
    background-position: -4097px 0px; }
.iti__flag.iti__ps {
    height: 10px;
    background-position: -4119px 0px; }
.iti__flag.iti__pt {
    height: 14px;
    background-position: -4141px 0px; }
.iti__flag.iti__pw {
    height:   15px;
    background-position: -4163px 0px; }
.iti__flag.iti__py {
    height: 11px;
    background-position: -4185px 0px; }
.iti__flag.iti__qa {
    height: 8px;
    background-position: -4207px 0px; }
.iti__flag.iti__re {
    height: 14px;
    background-position: -4229px 0px; }
.iti__flag.iti__ro {
    height: 14px;
    background-position: -4251px 0px; }
.iti__flag.iti__rs {
    height: 14px;
    background-position: -4273px 0px; }
.iti__flag.iti__ru {
    height: 14px;
    background-position: -4295px 0px; }
.iti__flag.iti__rw {
    height: 14px;
    background-position: -4317px 0px; }
.iti__flag.iti__sa {
    height: 14px;
    background-position: -4339px 0px; }
.iti__flag.iti__sb {
    height: 10px;
    background-position: -4361px 0px; }
.iti__flag.iti__sc {
    height: 10px;
    background-position: -4383px 0px; }
.iti__flag.iti__sd {
    height: 10px;
    background-position: -4405px 0px; }
.iti__flag.iti__se {
    height:   15px;
    background-position: -4427px 0px; }
.iti__flag.iti__sg {
    height: 14px;
    background-position: -4449px 0px; }
.iti__flag.iti__sh {
    height: 10px;
    background-position: -4471px 0px; }
.iti__flag.iti__si {
    height: 10px;
    background-position: -4493px 0px; }
.iti__flag.iti__sj {
    height: 15px;
    background-position: -4515px 0px; }
.iti__flag.iti__sk {
    height: 14px;
    background-position: -4537px 0px; }
.iti__flag.iti__sl {
    height: 14px;
    background-position: -4559px 0px; }
.iti__flag.iti__sm {
    height: 15px;
    background-position: -4581px 0px; }
.iti__flag.iti__sn {
    height: 14px;
    background-position: -4603px 0px; }
.iti__flag.iti__so {
    height: 14px;
    background-position: -4625px 0px; }
.iti__flag.iti__sr {
    height: 14px;
    background-position: -4647px 0px; }
.iti__flag.iti__ss {
    height: 10px;
    background-position: -4669px 0px; }
.iti__flag.iti__st {
    height: 10px;
    background-position: -4691px 0px; }
.iti__flag.iti__sv {
    height: 12px;
    background-position: -47  15px 0px; }
.iti__flag.iti__sx {
    height: 14px;
    background-position: -4735px 0px; }
.iti__flag.iti__sy {
    height: 14px;
    background-position: -4757px 0px; }
.iti__flag.iti__sz {
    height: 14px;
    background-position: -4779px 0px; }
.iti__flag.iti__ta {
    height: 10px;
    background-position: -4801px 0px; }
.iti__flag.iti__tc {
    height: 10px;
    background-position: -4823px 0px; }
.iti__flag.iti__td {
    height: 14px;
    background-position: -4845px 0px; }
.iti__flag.iti__tf {
    height: 14px;
    background-position: -4867px 0px; }
.iti__flag.iti__tg {
    height:   15px;
    background-position: -4889px 0px; }
.iti__flag.iti__th {
    height: 14px;
    background-position: -4911px 0px; }
.iti__flag.iti__tj {
    height: 10px;
    background-position: -4933px 0px; }
.iti__flag.iti__tk {
    height: 10px;
    background-position: -4955px 0px; }
.iti__flag.iti__tl {
    height: 10px;
    background-position: -4977px 0px; }
.iti__flag.iti__tm {
    height: 14px;
    background-position: -4999px 0px; }
.iti__flag.iti__tn {
    height: 14px;
    background-position: -5021px 0px; }
.iti__flag.iti__to {
    height: 10px;
    background-position: -5043px 0px; }
.iti__flag.iti__tr {
    height: 14px;
    background-position: -5065px 0px; }
.iti__flag.iti__tt {
    height: 12px;
    background-position: -5087px 0px; }
.iti__flag.iti__tv {
    height: 10px;
    background-position: -5109px 0px; }
.iti__flag.iti__tw {
    height: 14px;
    background-position: -5131px 0px; }
.iti__flag.iti__tz {
    height: 14px;
    background-position: -5153px 0px; }
.iti__flag.iti__ua {
    height: 14px;
    background-position: -5175px 0px; }
.iti__flag.iti__ug {
    height: 14px;
    background-position: -5197px 0px; }
.iti__flag.iti__um {
    height: 11px;
    background-position: -5219px 0px; }
.iti__flag.iti__un {
    height: 14px;
    background-position: -5241px 0px; }
.iti__flag.iti__us {
    height: 11px;
    background-position: -5263px 0px; }
.iti__flag.iti__uy {
    height: 14px;
    background-position: -5285px 0px; }
.iti__flag.iti__uz {
    height: 10px;
    background-position: -5307px 0px; }
.iti__flag.iti__va {
    height: 15px;
    background-position: -5329px 0px; }
.iti__flag.iti__vc {
    height: 14px;
    background-position: -5346px 0px; }
.iti__flag.iti__ve {
    height: 14px;
    background-position: -5368px 0px; }
.iti__flag.iti__vg {
    height: 10px;
    background-position: -5390px 0px; }
.iti__flag.iti__vi {
    height: 14px;
    background-position: -5412px 0px; }
.iti__flag.iti__vn {
    height: 14px;
    background-position: -5434px 0px; }
.iti__flag.iti__vu {
    height: 12px;
    background-position: -5456px 0px; }
.iti__flag.iti__wf {
    height: 14px;
    background-position: -5478px 0px; }
.iti__flag.iti__ws {
    height: 10px;
    background-position: -5500px 0px; }
.iti__flag.iti__xk {
    height: 15px;
    background-position: -5522px 0px; }
.iti__flag.iti__ye {
    height: 14px;
    background-position: -5544px 0px; }
.iti__flag.iti__yt {
    height: 14px;
    background-position: -5566px 0px; }
.iti__flag.iti__za {
    height: 14px;
    background-position: -5588px 0px; }
.iti__flag.iti__zm {
    height: 14px;
    background-position: -5610px 0px; }
.iti__flag.iti__zw {
    height: 10px;
    background-position: -5632px 0px; }

.iti__flag {
    height: 15px;
    box-shadow: 0px 0px 1px 0px #888;
    background-image: url("https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/18.1.6/img/flags.png");
    background-repeat: no-repeat;
    background-color: #DBDBDB;
    background-position: 20px 0; }
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .iti__flag {
        background-image: url("https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/18.1.6/img/<EMAIL>"); } }

.iti__flag.iti__np {
    background-color: transparent; }


.form .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 60px;
}

.form .select2-container--default .select2-selection--single {
    height: 62px;
    border-radius: 10px;
    border-color:rgba(1, 0, 0, .1);
}

.form .select2-container--default .select2-selection--single .select2-selection__arrow {
    top:18px;
}

.form {
    padding:15px;
}

.ft {
    float:left;
    margin-top: 20px;
    font-size:   14px;
    color:#888;
    margin-bottom: 30px;
}

.ft a {
    text-decoration: underline;
    color:#d02333;
}


.overlay-furniture,
.overlay-tools,
.overlay {
    position: relative; }

.overlay-furniture:before,
.overlay-tools:before,
.overlay:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background-color: rgba(0, 0, 0, 0.2); }

.overlay-tools:before {
    background-color: rgba(7, 28, 31, 0.5); }

.overlay-furniture:before {
    background-color: rgba(0, 35, 77, 0.5); }

.video-container {
    position: relative;
    z-index: 9; }

.video-tools {
    min-height: 600px; }

.video-section {
    overflow: hidden; }

.video-button,
.video-button:hover,
.video-button:focus {
    width: 140px;
    height: 140px;
    border: 2px solid #fefefe;
    -ms-display: flex;
    -webkit-display: flex;
    display: -webkit-box;
    display: flex;
    -ms-align-items: center;
    -webkit-align-items: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -ms-justify-content: center;
    -webkit-justify-content: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -ms-border-radius: 50%;
    -webkit-border-radius: 50%;
    border-radius: 50%;
    z-index: 300;


}

.video-section {
    position: relative;
    background: #2f4858;
    margin-top: 80px;
    padding-top: 0;
}

.video-section .bg-img {
    position: absolute!important;
    opacity: 0.5;
}

.video-button svg {
    width: 34px;
    height: 34px;
}

.video-button svg path {
    stroke: #fff;
}

.bg-titles {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 100%;
    margin:auto;
    color:#fff;
}

.bg-titles h2 {

    font-family: p_bold, sans-serif;
    font-weight: 700;
    margin-bottom: 30px;
    font-size:24px;
}

.bg-titles p {
    line-height: 1.7;
    color:#fff;
    text-align: center;
}

.bg-titles a {
    padding:15px 30px;
    border-radius: 10px;
    color:#fff;
    background: #d02333;
    margin-top:30px;
}


.experience
.exp-number {
    background-image: url("../img/bg-2.svg");
    color: transparent;
    -webkit-text-stroke: 1.5px var(--heading-color);
    -webkit-text-fill-color: transparent;
    -webkit-background-clip: text;
    background-clip: text;
    transform: scale3d(1, 1.2, 1);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    max-width: 570px;
    font-size: 1  15px;
    font-weight: 600;
    line-height: 90px;

}

/*----------------------------------------*/
/*  16. ABOUT CSS START
/*----------------------------------------*/
.about__area {
    position: relative;

}
.about__area .shape {
    position: absolute;
    top: 65px;
    left: 48%;
    border: none;
}
@media (max-width: 1399px) {
    .about__area .shape {
        left: 56%;
    }
}
@media (max-width: 1199px) {
    .about__area .shape {
        left: 5%;
        top: 34%;
    }
}
@media (max-width: 991px) {
    .about__area .shape {
        top: 120px;
        left: 10%;
        max-width: 70px;
    }
}
@media (max-width: 767px) {
    .about__area .shape {
        top: 180px;
        left: 29%;
    }
}
.about__left {
    position: relative;
}
.about__left img {
    border: 5px solid #eeeeee;
    border-radius: 15px;
    overflow: hidden;
}
@media (max-width: 991px) {
    .about__left img {
        border-width: 3px;
    }
}
.about__left .image {
    -webkit-transform: translateX(-50px);
    transform: translateX(-50px);
}
@media (max-width: 1199px) {
    .about__left .image {
        max-width: 300px;
        -webkit-transform: translateX(-30px);
        transform: translateX(-30px);
    }
}
@media (max-width: 991px) {
    .about__left .image {
        max-width: 220px;
    }
}
@media (max-width: 767px) {
    .about__left .image {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
}
.about__left .image-2 {
    -webkit-transform: translateY(-50px);
    transform: translateY(-50px);
}
@media (max-width: 1199px) {
    .about__left .image-2 {
        max-width: 300px;
    }
}
@media (max-width: 991px) {
    .about__left .image-2 {
        max-width: 220px;
    }
}
@media (max-width: 767px) {
    .about__left .image-2 {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
}
.about__left .image-3 {
    position: absolute;
    bottom: 120px;
    left: 29%;
}
@media (max-width: 1919px) {
    .about__left .image-3 {
        left: 20%;
    }
}
@media (max-width: 1199px) {
    .about__left .image-3 {
        bottom: 200px;
        max-width: 280px;
    }
}
@media (max-width: 991px) {
    .about__left .image-3 {
        bottom: unset;
        top: 30px;
        max-width: 200px;
    }
}
@media (max-width: 767px) {
    .about__left .image-3 {
        left: unset;
        right: 15px;
    }
}
.about__left-2 {
    text-align: center;
    position: relative;
}
@media (max-width: 767px) {
    .about__left-2 {
        margin-top: 50px;
        padding-bottom: 30px;
    }
}
.about__left-2::after {
    position: absolute;
    content: "+";
    width: 130px;
    height: 130px;
    top: 180px;
    margin-left: 90px;
    font-weight: 700;
    font-size: 150px;
    z-index: 9;
    color: var(--black-3);
    background: var(--white);
    border-radius: 100%;
    line-height: 0.8;
}
@media (max-width: 1399px) {
    .about__left-2::after {
        top: 165px;
    }
}
@media (max-width: 1199px) {
    .about__left-2::after {
        width: 110px;
        height: 110px;
        top: 135px;
        margin-left: 80px;
        font-size: 130px;
    }
}
@media (max-width: 991px) {
    .about__left-2::after {
        width: 90px;
        height: 90px;
        top: 95px;
        font-size: 110px;
        margin-left: 45px;
    }
}
.about__left-2 .exp {
    color: #d02333;
    font-weight: 700;
    font-size: 500px;
    line-height: 0.8;
    background: -webkit-gradient(linear, left bottom, left top, from(var(--secondary)), to(var(--primary)));
    background: linear-gradient(to top, var(--secondary) 0%, var(--primary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
@media (max-width: 1399px) {
    .about__left-2 .exp {
        font-size: 460px;
    }
}
@media (max-width: 1199px) {
    .about__left-2 .exp {
        font-size: 380px;
    }
}
@media (max-width: 991px) {
    .about__left-2 .exp {
        font-size: 280px;
    }
}
.about__left-2 .exp-title {
    font-size: 60px;
    color: var(--black-2);
    font-weight: 700;
    line-height: 1;

}
@media (max-width: 1399px) {
    .about__left-2 .exp-title {
        font-size: 52px;
    }
}
@media (max-width: 1199px) {
    .about__left-2 .exp-title {
        font-size: 45px;
    }
}
@media (max-width: 991px) {
    .about__left-2 .exp-title {
        font-size: 32px;
    }
}
.about__right {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    height: 100%;
    padding-left: 15px;
}
@media (max-width: 767px) {
    .about__right {
        padding-left: 0;
        padding-top: 40px;
    }
}
.about__right p {
    padding-top: 15px;
}
@media (max-width: 991px) {
    .about__right p {
        padding-top: 0;
    }
}
@media (max-width: 991px) {
    .about__right-2 br {
        display: none;
    }
}
@media (max-width: 767px) {
    .about__right-2 {
        padding-top: 20px;
    }
}
.about__right-2 .list {
    display: -ms-grid;
    display: grid;
    -ms-grid-columns: 1fr 30px 1fr;
    grid-template-columns: 1fr 1fr;
    grid-gap: 30px;
    padding-top: 50px;
    padding-bottom: 50px;
}
@media (max-width: 991px) {
    .about__right-2 .list {
        grid-gap: 20px;
        padding-top: 30px;
        -ms-grid-columns: 1fr;
        grid-template-columns: 1fr;
    }
}
@media (max-width: 767px) {
    .about__right-2 .list {
        grid-gap: 10px;
        padding-top: 30px;
    }
}
.about__right-2 .list li {
    display: -ms-grid;
    display: grid;
    grid-gap: 15px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -ms-grid-columns: 50px 15px auto;
    grid-template-columns: 50px auto;
}
.about__right-2 .list li .icon {
    padding: 8px;
    -webkit-box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    background-color: var(--white);
}
.about__right-2 .list li span {
    font-size: 16px;
    font-weight: 700;
    color: var(--black-3);
}
.about__right-2 p {
    line-height: 1.7;
    padding-right: 60px;
}
@media (max-width: 991px) {
    .about__right-2 p {
        padding-right: 0;
    }
}

.dark {
    background-color: var(--dark);
}
.dark.about__area {
    display: inline-block;
    width: 100%;
}
.dark .about__text {
    color: var(--white);
}
.dark .about__right-2 .list li span {
    color: var(--gray-7);
}
.dark .about__right-2 .list li .icon {
    -webkit-box-shadow: none;
    box-shadow: none;
    background-color: var(--dark-3);
}
.dark .about__left-2 .exp-title {
    color: var(--white);
}

.about__right-inner ul {
    display: -ms-grid;
    display: grid;
    -ms-grid-columns: 1fr 20px 1fr;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 20px;
    padding-top: 25px;
    padding-bottom: 50px;
}
@media (max-width: 1199px) {
    .about__right-inner ul {
        -ms-grid-columns: 1fr;
        grid-template-columns: 1fr;
        grid-gap: 15px;
        padding-top: 30px;
    }
}
.about__right-inner ul li {
    font-size: 16px;
    position: relative;
    padding-left: 35px;
    display: flex;
    align-items: center;
}
@media (max-width: 991px) {
    .about__right-inner ul li {
        font-size: 16px;
    }
}
.about__right-inner ul li::before {
    position: absolute;
    content: "\f00e";
    left: 0;
    width: 25px;
    height: 25px;
    font-weight: 700;
    font-family: "Icofont";
    font-size: 11px;
    border: 1px solid #d02333;
    border-radius: 100px;
    color: #d02333;
    text-align: center;
    line-height: 1.75px;
}
.list-check-2 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    gap: 25px;
}
@media (max-width: 1399px) {
    .list-check-2 {
        gap: 20px;
    }
}
@media (max-width: 991px) {
    .list-check-2 {
        gap: 15px;
    }
}
.list-check-2 li {
    font-size: 18px;
    color: var(--black-2);
    position: relative;
    padding-left: 35px;
}
@media (max-width: 1199px) {
    .list-check-2 li {
        font-size: 16px;
        padding-left: 25px;
    }
}
@media (max-width: 991px) {
    .list-check-2 li {
        font-size: 14px;
    }
}
@media (max-width: 767px) {
    .list-check-2 li {
        font-size: 16px;
    }
}
.list-check-2 li::before {
    position: absolute;
    content: "\f058";
    left: 0;
    width: 25px;
    height: 25px;
    font-weight: 700;
    color: #d02333;
    font-family: "Font Awesome 6 Free";
}
.list-check-2 li.disabled {
    color: var(--gray-6);
}
.list-check-2 li.disabled::before {
    color: var(--gray-2);
}


/*----------------------------------------*/
/*  06. TITLE CSS START
/*----------------------------------------*/
.sec-title {
    font-size: 14px;
    padding-bottom: 20px;
    font-weight: bold;
    line-height: 1.7;
}
@media (max-width: 1399px) {
    .sec-title {
        font-size: 14px;
    }
}
@media (max-width: 1199px) {
    .sec-title {
        font-size: 14px;
    }
}


.sec-sub-title {
    font-size: 24px;
    color: #d02333;
    font-weight: 300;
    padding-bottom: 10px;
    line-height: 1.7;
    font-weight: bold;
    display: flex;
    align-items: center;
    font-family: 'p_bold', sans-serif;
}

.sec-sub-title:before {
    content: "";
    width: 53px;
    height: 2px;
    background: #d02333;
    float: left;
    margin-right: 30px;
}

.dark p {
    color: #999;
}
.dark .sec-title {
    color: var(--white);
}


.cxu-btn-primary {
    padding:15px 30px;
    border-radius: 10px;
    color:#fff;
    background: #d02333;
    float:left;
    margin-top:30px;

}

.about__right-inner p {
    line-height: 1.7;
    color:#888;
}

.sec-title span {
    padding-left:10px;
    color:#d02333
}

.countdowns {
    padding:50px 0;
    margin-top:80px;
}

.fl-2 {
    flex-direction: column!important;
}

.fl-2  p {
    font-size: 14px!important;
}


.countdowns .row {
    display: flex;
    align-items: center;
}

.countdown {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.countdown .item-c {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    border-radius:10px;
    padding:30px;
    background:#fff;
    box-shadow:15px 20px 30px rgba(1,0,0,.1);
    position: relative;
}

.countdown .item-c:after {
    content: "";
    width:10px;
    height: 10px;
    background:#d02333;
    border-radius: 50%;
    position: absolute;
    bottom:-5px;
}

.countdown .item-c strong {
    font-size:34px;
    margin-bottom: 15px;
    font-family: p_bold, sans-serif;
    font-weight: 700;

}

.countdown .item-c span {

}
.countdowns .section-title {
    padding-right:50px;
}
.countdowns .section-title a {
    padding: 15px 30px;
    border-radius: 10px;
    color: #fff;
    background: #d02333;
    margin-top: 30px;
    float:left;
}


.cart {
    position: absolute;
    top: 0;
    right: 0;
    width: 550px;
    display: flex;
    flex-direction: column;
    background: #fff;
    padding: 20px;
    border-radius: 15px;
    top: 75%;
    box-shadow: 0px 0px 30px rgba(1,0,0,.1);
    z-index: 900;
}


.cart-title {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px dashed #f2f2f2;
}


.cart-title h2 {
    width: 100%;
    display: flex;
    justify-content: space-between;
}

.cart-title h2 a {
    color:#d02333;
}

.c {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.c-t {
    width: calc(100% - 100px);
    float: left;
    border-right: 1px solid #f2f2f2;
    margin-right: 30px;
}

.c-t .title-init {
    display: flex;
    align-items: center;
}

.c-t__infos {
    width: 100%;
    float:left;
    margin-top: 20px;
}
.c-t__infos .country {
    display: flex;
    align-items: center;
    float:left;
    font-size: 14px;
    margin-right: 15px;
    padding-right: 15px;
    border-right:1px solid #dedede;
}
.c-t__infos .country img {
    width: 20px;
    height: 20px;
    overflow: hidden;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 10px;
}

.c-t__infos .country h2 {
    font-size: 14px;
}
.c-t__infos .date span {
    display: flex;
    align-items: center;
}

.c-t__infos .date svg {
    width: 20px;
    height: 20px;
    float:left;
    margin-right: 10px;
}

.header {
    z-index: 900;
}

.c-t .title-init figure {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #d02333;
    border-radius: 10px;
    margin-right: 20px;
}
.c-t .title-init figure img {
    height: 36px;
}


.c-title__tile {
    width: calc(100% - 80px);
    float: left;
}

.c-title__tile h3 {
    line-height: 1.5;
}

.c-info .quantify {
    float: left;
    display: flex;
    align-items: center;
}

.c-info .qt,
.c-info .qt-minus,
.c-info .qt-plus {
    float: left;
    font-family: "p_bold"
}

.c-info .qt {
    font-size: 14px;
    line-height: 1.75px;
    width: 40px;
    text-align: center;
}

.c-info .qt-minus,
.c-info .qt-plus {
    background: #fcfcfc;
    border: none;
    width: 25px;
    height: 25px;
    font-size: 8px;
    background: #f2f2f2;
    border-radius: 0;
    color: #111;
    display: flex;
    align-items: center;
    justify-content: center;
}

.c-info .qt-minus:hover,
.c-info .qt-plus:hover {
    background: #d02333;
    color: #fff;
    cursor: pointer;
}

.c-info {
    display: flex;
    align-items: center;
    flex-direction: column;
    align-items: center;
    text-align: center;
    justify-content: center;
}

.c-info  p {

    margin-bottom: 20px;
    font-family: "p_bold";
    font-weight: 700;
    font-size: 18px;
}

.title-init {
    position: relative;
}


.title-init span {
    color:#4f4f4f;
    font-family: "p_regular"!important;
}

.delete-it {
    position: absolute;
    top:-10px;
    left:-3px;
    cursor: pointer;
}

.delete-it svg {
    width: 24px;
    height: 24px;
    fill:red;
}

.delete-it svg path {
    fill:#d02333;;
}

.c {
    padding-bottom: 15px;
    margin-bottom: 15px;
    float:left;
    width: 100%;
    border-bottom:1px solid #f2f2f2;
}

.c:last-child {
    padding-bottom: 0;
    margin-bottom: 0;
    border:none;
}

.all-sum {
    width:100%;
    float:left;
    padding:15px 30px;
    background:#f2f2f2;
    margin-top: 20px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.all-sum p {
    font-weight: 700;
    font-family: "p_bold";
}

.register-now {
    padding:15px 30px;
    border-radius: 10px;
    cursor: pointer;
    background: #d02333;
    margin-top: 30px;
    width: 240px;
    color:#fff;
    float:right;
    text-align: center;
}

.cart {
    display: none;
}

.cart .active-cart {
    display: block;
}


.nocart {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.nocart svg {
    width:250px;
    height: 200px;
    fill:#d02333;

}
.nocart svg g{

    fill:#d02333;

}
.nocart svg path{

    fill:#d02333;

}


.nocart p {
    font-size:  15px;
    padding-top:15px;
    color:#b71234
}

.cover-img__bg {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.5;
}


#cart-modal {
    position: absolute;
    right: 0;
    top: 77%;
}

.cart-block .section-title h2 {
    text-align: left;
    font-family: "p_bold";

}

.all {
    background: #fff;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0px 0px 30px rgba(1,0,0,.1);
}

.ch {
    position: relative;
}

.steps {
    width: 100%;
    float:left;
}

.steps-lists {
    width: 100%;
    float:left;
    display:flex;
    align-items: center;
    justify-content: space-between;
}

.steps-lists__item {
    width: calc(100% / 3);
    float:left;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 30px;
}

.steps-lists__item:last-child {
    padding-right: 0;
}

.steps-lists__item p {
    font-family: "p_bold";
    font-size: 16px;
    color:#888;

}
.steps-lists__item:before {
    content: "";
    width: 30px;
    height: 30px;
    background:#dedede;
    border-radius: 50%;
    float:left;
}

.steps-lists__item:after {
    content: "";
    width: 40%;
    float: right;
    height: 2px;
    background:#dedede;
    border-radius: 5px;

}
.steps-lists__item.active p {
    color:#d02333;
}
.steps-lists__item.active:before , .steps-lists__item.active:after{
    background: #d02333;
}

.promo {
    margin-top:30px;
}

.all h1 {
    font-family: "p_bold";

}

.c-form {
    padding:30px;
    background:#fff;
    float:left;
    width:100%;
    border-radius: 15px;
    box-shadow: 0px 0px 30px rgba(1,0,0,.1);
}

.c-form .loginform-form input {
    padding:12px 20px;
    border-radius: 5px;
    border:2px solid #f2f2f2;
}
.steps .nav-tabs {
    border:none;
}
.steps .nav-tabs .nav-item {
    margin-bottom: -1px;
    width: calc(100% / 3);
    border:none;
    background: none;
}

.steps .steps-lists__item {
    width:100%;
}

.nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active {
    background: none;
    border:none;
    color:#d02333;

    font-family: "p_bold";
}

.nav-tabs .nav-link {
    color:#242424;

    font-family: "p_bold";
    box-shadow: none;
    border:none;
    outline: none;
    padding-right: 30px;
}


.choose .custom-control {
    display: flex;
    align-items: center;
}

.choose {
    margin-bottom: 40px;
    margin-top: -10px;
}

.choose-title {
    margin-bottom: 20px;
}

.modal-body__form .loginform-form input {
    padding:12px 20px;
    border:2px solid #f2f2f2;
    border-radius: 5px;
}

.l-c .l-item__img {
    width: 50%;
}

.not-found {
    width:100%;
    float:left;
    background: #FFF4F1;
    border-radius: 15px;
    padding:30px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.not-found svg {
    width: 300px;
    height: 300px;
    margin-bottom: 30px;
}

.not-found h3 {
    font-family: "p_bold";
    font-size: 21px;
    margin-bottom: 30px;
}

.not-found a {
    color:#242424;
}

.not-found a:hover {
    color:#d02333;
}

.not-found {
    margin-bottom: 40px;
    padding-bottom: 80px;
}


.q-second {
    position: absolute;
    bottom:30px;
    left:30px;
    transform: rotate(180deg);
}

.q-second svg {
    width: 80px;
    height: 80px;
    fill: #fff;
}

.header-full .au {
    color:#fff;
}

.au {

}

#courses {
    position: fixed;
    width: 360px;
    top: 0;
    right: 360px;
    background: #fff;
    z-index: 900;
    display: none;
    height: 100vh;
    padding: 40px 20px;
    overflow-y: scroll;
    box-shadow: 0px 0px 30px rgba(1,0,0,.1);
}


#courses::-webkit-scrollbar {
    width: 5px
}

#courses::-webkit-scrollbar-track {
    background: #fff
}

#courses::-webkit-scrollbar-thumb {
    background: #d02333
}



.all-courses__btn:not(:hover) + #courses {
    display: none;
}

#cart-body {
    max-height: 340px;
    overflow-y: scroll;
    padding-right: 15px;
    padding-top: 15px;
}

#cart-body::-webkit-scrollbar {
    width: 5px
}

#cart-body::-webkit-scrollbar-track {
    background: #fff
}

#cart-body::-webkit-scrollbar-thumb {
    background: #d02333
}


.title-on__it {
    margin-bottom: 40px;
}

.inv svg {
    width: 21px;
    height: 21px;
    position: absolute;
    right: 35px;
    fill:#fff;
}

.inv {
    margin-bottom: 10px;
    width: 100%;
    float:left;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    border-radius: 15px;
    min-height: 54px;
    margin-top:25px;
    background:#d02333;
    position: relative;
}

.agreement {
    float:left;
    width: 100%;
    margin-top:20px;
    margin-bottom: 30px;
}

.agreement .custom-control {
    display: flex;
    align-items: center;
}
.agreement .custom-control a {
    color:#d02333;
}
.news-full:after {
    display: none;
}

.news-full__img {
    height:450px;
    overflow: hidden;
    border-radius: 20px;
}

.invoice-item {
    width: 100%;
    float:left;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.invoice-item svg {
    width: 300px;
    height: 300px;
}


.invoice-item  h3 {
    font-family: "p_bold";
    color:#d02333;

    margin-bottom: 30px;
}
.invoice-item  p {
    line-height: 1.7;
    color:#999;

}

.invoice-item a {
    padding:15px 30px;
    border-radius: 10px;
    color:#fff;
    background:#d02333;
    margin-top:30px;
    width: 240px;
    text-align: center;

}

.hero-slid .hero-slider__img {
    width: 100%;
    border-radius: 0;
}

.hero-slid .hero-slider__title {
    position: absolute;
    top:0;
    width: 100%;
    z-index: 400;
}

.hero-slider_it {
    width: 100%;
    height: 500px;
    position: relative;
    background:#242424;
}

.hero-slider_it img {
    width: 100%;
    height: 100%;
    position: absolute;
    object-fit: cover;
    opacity: 0.5;
}

.hero-slid .hero-slider__title h1 , .hero-slid .hero-slider__title p  {
    color:#fff;
    opacity: 1;
}

.hero-slid .hero-slider__title a {
    background: #d02333;
    min-width: 230px;
}

.hero-slid .counter {
    z-index: 400;
    bottom:10px;
    color:#fff;
}

.hero-slid {
    height: auto;
}

.hero-slider_it:before {
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    background-image: linear-gradient(to right, #2f4858 , transparent);
    left:0;
    z-index: 300;
}

.show-all__course {
    display: flex;
    align-items: center;
    border-radius:10px;
    cursor: pointer;
    background:#d02333;
    height: 45px;
    color:#fff!important;
    padding:0 20px;
}

.show-all__course svg {
    margin: 0;
    width: 19px;
    height: auto;
    margin-right: 15px;
}
.show-all__course svg {
    fill:#fff;
}
.show-all__course svg path {
    fill:#fff;
}

.header-menu__logo {
    align-items: center;
}

.about {
    padding-top: 40px;
}

.header.fixed .header-menu__logo .show-all__course svg {
    margin-left: 0;
}



#courses .courses-slider__item {
    padding:0;
    margin-bottom: 15px;
}

.title-on__it h1 {
    color:#d02333;
    display: flex;
    align-items: center;

    font-size: 18px;
}

.title-on__it svg {
    width: 29px;
    height: 29px;
    float:left;
    margin-right: 20px;
    fill:#d02333;
}

#courses .close-it {
    width: 50px;
    height: 50px;
    overflow: hidden;
    left: 360px;
    position: fixed;
    cursor: pointer;
    top: 30px;


}

#courses  .close-it svg {
    width: 50px;
    height: 50px;

}

#courses  .close-it svg line {
    stroke: #fff;
}

.us-item__title {
    margin-bottom: 30px;
}

.us-item__title h2 {
    line-height: 1.7;
}

.partners-full .section-title {
    display:flex;
    justify-content:center;
    flex-direction: column;
    align-items: center;

}

.partners-full {
    border-top:1px solid #dedede;
    padding-top:0;
    margin-top:80px;
}

.partners-full .icon svg {
    width: 160px;
    height: 160px;
    position: absolute;
    left: 5px;
    top: -40px;

}

.partners-full .icon svg path {
    fill:#d02333
}

.partners-full .icon {
    position: relative;
    width: 100px;
    height: 50px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fbfbfb;
    margin-top:-25px;
}

.partners-full .section-title h1 {
    text-align: center;
    margin-bottom: 40px;
    display: flex;
    justify-content: center;
}
.partners-full .section-title h1:before {
    display: none;
}
.courses-full .section-title h1 {
    color:#2f4858;
    font-size: 18px;
    margin-top:0;
}

.courses-full .section-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.courses-full .section-title  ul {
    width: auto;
    display: flex;
    margin-top:0;
}

.courses-full .section-title  ul li a {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border:1px solid #ececec;
    display: flex;
    align-items: center;
    justify-content: center;
    padding:0;
    color:#242424;
    font-size: 16px;
}

.courses-full .section-title  ul li a:hover {
    color:#fff;
    border-color:#2f4858;
    background:#2f4858;
}

.burger-open svg {
    margin-left:0;
}

.burger-open {
    margin-right: 0px;
    margin-left:30px!important;
    float:left;
}

.header.fixed .header-menu__logo .burger-open svg {
    margin-left:0;
}

.burger-open svg path {
    stroke:none!important;
}


#icon, icon2 {
    width: 45px;
    height: 45px;
    position: relative;
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-transition: .5s ease-in-out;
    -moz-transition: .5s ease-in-out;
    -o-transition: .5s ease-in-out;
    transition: .5s ease-in-out;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 30px;
    margin-top: 15px;
    cursor: pointer;
}
#icon span {
    display: block;
    position: absolute;
    height: 6px;
    width: 6px;
    background: #2f4858;
    opacity: 1;
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-transition: .25s ease-in-out;
    -moz-transition: .25s ease-in-out;
    -o-transition: .25s ease-in-out;
    transition: .25s ease-in-out;
}
#icon:hover span {
    background: #d02333;
}
#icon span:nth-child(even) {
    left: 25%;
    border-radius: 50%;
}

#icon span:nth-child(odd) {
    left:0px;
    border-radius: 50%;
}
#icon span:nth-child(1), #icon span:nth-child(2), #icon span:nth-child(3) {
    top: 0px;
}
#icon span:nth-child(4), #icon span:nth-child(5), #icon span:nth-child(6) {
    top: 10px;
}
#icon span:nth-child(7), #icon span:nth-child(8), #icon span:nth-child(9) {
    top: 20px;
}
#icon span:nth-child(3) {
    left: 50%;
}

#icon span:nth-child(6) {
    left: 50%;
}

#icon span:nth-child(9) {
    left: 50%;
}

#icon.open span:nth-child(1), #icon.open span:nth-child(2), #icon.open span:nth-child(3) {
    top: 10px;
}

#icon.open span:nth-child(7), #icon.open span:nth-child(8), #icon.open span:nth-child(9) {
    top: 10px;
}

.menu-popup {
    width: 360px;
    height: 100vh;
    position: fixed;
    right:0;
    top:0;
    border-left:1px solid #dedede;
    z-index: 900;
    background: #fff;
    padding:30px;
    right:-360px;
    opacity: 0;
    visibility: hidden;
    transition: 0.3s all ease-in;
}

.menu-popup.active {
    right:0;
    opacity: 1;
    visibility: visible;
}

.right-fixed {
    position: absolute;
    right:300px;
}

.menu-popup h2 {
    color:#d02333;
    margin-bottom: 40px;
    font-size: 18px;
    margin-top:10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.menu-popup h2 a {
    color:#d02333;
    font-size: 30px;
}

.menu-popup  ul {
    width: 100%;
    float:left;

}


.menu-popup  ul li {
    width: 100%;
    float:left;
    margin-bottom: 20px;
}

.menu-popup  ul li a {
    font-size:16px;
    color:#242424;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    float: left;
    background: #d02333;
    padding: 15px 30px;
    border-radius: 10px;
    color: #fff;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;

}

.menu-popup  ul li a svg {
    width:24px;
    height: 24px;
}

.menu-popup  ul li:hover ul {
    display: block;
    position: relative;
    border:none;
    padding:0;
    margin-top:20px;
}
.menu-popup  ul li ul li:last-child {
    margin-bottom: 0;
}

.menu-popup  ul li ul li a:hover {
    background: none;
    color:#d02333;
}

.menu-popup  ul li ul li a {
    font-size: 14px;
}

.menu-popup ul li ul li a {
    background: none;
    color:#242424;
}


.all-courses__btn  {
    width: 100%;
    float: left;
    background: #d02333;
    padding: 15px 30px;
    border-radius: 10px;
    margin-bottom: 20px;
    color: #fff;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}

.all-courses__btn  svg {
    width: 24px;
    height: 24px;
}

.all-courses__btn svg path {
    fill: #fff;
}

.header-full.fixed .header-menu__actions a svg path {
    stroke:#242424;
}
.header-full.fixed  .header-menu__actions a:last-child svg path {
    stroke: transparent;
    fill:#242424;
}

.about-item__title {
    position: relative;
    z-index:300;
}

.about-item__title figure img {
    width: 48px;
    height: 48px;
    object-fit: contain;
}

.about-item__title a {
    border: 1px solid #d02333;
    padding: 10px 16px;
    border-radius: 10px;
    transition: 0.3s all ease-in;
    float:left;
    margin-top:20px;
    color: #d02333;
}
.about-item__title a:hover {
    background: #d02333;
    color:#fff;
}
.courses .spead {
    margin-top:40px;
}

.about-item , .about-item__title {
    width: 100%;
    float:left;
}

.menu-popup ul li ul li a {
    padding:0 30px;
}

.v-title {
    position: absolute;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    z-index: 300;
    justify-content: center;
    height: calc(100vh - 180px);
    padding-top:50px;
}
.v-title h1 {
    margin-bottom:20px;
}

.v-title p {
    color:#fff;
    font-size: 14px;
    max-width:60%;
    line-height: 1.7;
}

.v-title a {
    color: #fff;
    padding: 15px 30px;
    border-radius: 10px;
    transition: 0.3s all ease-in;
    margin-top:30px;
    background: #d02333;;
}

.header-full #icon span {
    background:#fff;
}.header-full.fixed #icon span {
     background:#242424;
 }



#my-modal .modal-body {
    padding:40px;
}
#my-modal .modal-header {
    padding:20px 40px;
}
.imgs {
    width: 250px;
    height: 250px;
    overflow: hidden;
    border-radius: 50%;
    position: relative;
}

.imgs figure {
    width: 100%;
    height: 100%;
}

.imgs figure img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
#small {
    font-size: 13px;
    margin-bottom: 10px;
    color:#fff;
}


#exampleModalLabel {
    font-family: 'p_bold', sans-serif;
    margin-bottom:10px;
    color:#242424;
}

#title {
    font-size: 13px;
    line-height: 1.5;
    color:#fff;
}

#text {
    font-size: 14px;
    line-height: 1.5;
    margin-top: 50px;
    width: 100%;
}
#text p {
    margin-bottom: 20px;
    width: 100%;
    float:left;
    line-height: 2;
}

#text h2 {
    width: 100%;
    float:left;
    margin-bottom: 20px;
    font-size: 24px;
}


#my-modal .modal-dialog {
    max-width: 800px;
}

.top-title {
    display: flex;
    align-items: center;
}

.top-title__title {
    width: calc(100% - 250px);
    background: rgb(208, 35, 51);
    padding: 40px;
    position: relative;
    border-radius:20px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.top-title__title:after {
    content: "";
    width: 100px;
    height: 100%;
    position: absolute;
    right:-80px;
    top:0;
    background: rgb(208, 35, 51);
    border-radius:20px;

}


.why_us-item figure img {
    width:48px;
    height:48px;
    object-fit: contain;
}

.courses-full__tabs {
    margin-top: -5px;
}

.price-mob {
    display: none;
}

/*----------------------------------------*/
/*  . CONTACT CSS START
/*----------------------------------------*/
.contact__item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    text-align: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    background: #fff;
    border-radius: 10px;
    padding: 45px;
    margin-bottom: 40px;
}
.contact__item .title {
    font-weight: 600;
    font-size: 20px;
    line-height: 1.2;
    text-align: center;
    color: #333F4D;
    padding-top: 20px;
    padding-bottom: 15px;
}
.contact__item p {
    font-weight: 500;
    font-size: 16px;
    line-height: 2;
    color: #67687A;
}
.contact__item a {
    font-weight: 500;
    font-size: 16px;
    line-height: 2;
    color: #67687A;
}
.contact__submit {
    display: inline-block;
    padding: 20px 50px;
    font-weight: 600;
    font-size: 14px;
    line-height: 1.5;
    letter-spacing: 1.6px;
    text-transform: uppercase;
    color: #FFFFFF;
    border: 0;
    background: #d02333;
    cursor:pointer;
    border-radius: 5px;
}

.pt-30 {
    padding-top: 30px;
}

.blog__detailleft {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    gap: 30px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.blog__social {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    gap: 10px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.blog__social li a {
    font-weight: 400;
    font-size: 16px;
    line-height: 1;
    color: #D8FF36;
    padding: 10px 10px;
    background: #F4F5FA;
    border-radius: 5px;
    display: inline-block;
}
.blog__submitbtn {
    display: inline-block;
    padding: 20px 30px;
    background: #D8FF36;
    border-radius: 5px;
    font-size: 14px;
    line-height: 1.5;
    letter-spacing: 1.6px;
    text-transform: uppercase;
    color: #FFFFFF;
    border: 0;
}
.blog__form {
    padding-top: 90px;
}
@media (max-width: 767px) {
    .blog__form {
        padding-top: 30px;
    }
}
.blog__form form {
    padding-top: 30px;
}
.blog__form-title {
    font-weight: 600;
    font-size: 20px;
    line-height: 1.4;
    color: #0E1E2A;
    display: block;
}
.blog__form input {
    width: 100%;
    padding: 20px;
    border: 1px solid #E3E3E3;
    border-radius: 5px;
    height: 51px;
    margin-bottom: 20px;
}
.blog__form input:focus {
    outline: 0;
}
.blog__form textarea {
    height: 180px;
    width: 100%;
    padding: 20px;
    border: 1px solid #E3E3E3;
    border-radius: 5px;
    margin-bottom: 20px;
}
.blog__form textarea:focus {
    outline: 0;
}
.blog__submitwrap {
    text-align: center;
}
@media (max-width: 991px) {
    .blog__submitwrap {
        margin-bottom: 30px;
    }
}

.contact__item i {
    font-size:36px;
}

.contact__area-6 iframe {
    border-radius:10px;
}




.show-filter {
    width: 100%;
    float:left;
    display: flex;
    align-items: center;
    justify-content: center;
    background:#d02333;
    padding:15px 30px;
    border-radius:10px;
    margin-bottom:30px;
    color:#fff;
    cursor: pointer;
    display: none;
}


.show-filter svg {
    width: 24px;
    height: 24px;
    margin-right:10px;
}

.courses-slider__item .title-in {
    width:calc(100% - 60px)
}

.courses-slider__item a figure img {
    height: 30px;
}

.courses-full .section-title ul {
    width: auto;
    display: flex;
    margin-top: 0;
    position: absolute;
    right: 0;
    top: -3px;
}

.countdowns .section-title {
    display: block;
}

.blog-content strong {
    font-weight:bold;
}
.blog-content em {
    font-style: italic;
}
.blog-content u {
    text-decoration: underline;
}
.blog-content ol {
    list-style-type: number;
    padding-left:30px;
    margin-bottom: 20px;
    width: 100%;
    float:left;
}

.blog-content ul {
    list-style-type: disc;
    padding-left:30px;
    margin-bottom: 20px;
    width: 100%;
    float:left;
}

.blog-content ul li, .blog-content ol li {
    width:100%;
    float:left;
    margin-bottom: 10px;
}
.blog-content ul li:last-child, .blog-content ol li:last-child {
    margin-bottom: 0;
}
.blog-content h2 , .blog-content h3 {
    width: 100%;
    float:left;
    margin-bottom: 15px;
    font-weight: bold;
}


/*--
/*  19 - Blog Details CSS
/*----------------------------------------*/
.blog-details-section .blog-details-wrap {
    margin-top: -70px;
}

.blog-details-post {
    margin-top: 70px;
}

.blog-details-post .single-blog-post {
    margin-top: 40px;
}

.blog-details-post .single-blog-post.single-blog .blog-image a {
    display: block;
}

.blog-details-post .single-blog-post.single-blog .blog-image a img {
    width: 100%;
    border-radius: 10px;
}

hr {
    width: 100%;
    float:left;
}

.blog-details-post .single-blog-post.single-blog .blog-content {
    padding: 40px 0px 0px;
    width: 100%;
    left: 0;
    bottom: 0px;
    transform: translateX(0);
    margin-top: 0px;
}

.blog-details-post .single-blog-post.single-blog .blog-content .title {
    font-size: 30px;
    line-height: 40px;
}

.blog-details-post .single-blog-post.single-blog .blog-content .text {
    font-size: 16px;
    line-height: 30px;
    margin-top: 20px;
    padding-bottom: 40px;
}

.blog-details-post .blog-details-content .blog-quote {
    background-color: #fdf8f4;
    border-left: 5px solid #d02333;
    border-radius: 5px;
    margin-top: 40px;
}

.blog-details-post .blog-details-content .blog-quote .blockquote {
    padding: 50px 80px 35px 35px;
    margin-bottom: 0;
    position: relative;
}

@media only screen and (max-width: 1199px) {
    .blog-details-post .blog-details-content .blog-quote .blockquote {
        padding: 50px 50px 35px 35px;
    }
}

@media only screen and (max-width: 449px) {
    .blog-details-post .blog-details-content .blog-quote .blockquote {
        padding: 50px 35px 35px 35px;
    }
}

.blog-details-post .blog-details-content .blog-quote .blockquote:not(:first-child) {
    margin-top: 25px;
}

.blog-details-post .blog-details-content .blog-quote .blockquote:not(:last-child) {
    margin-bottom: 25px;
}

.blog-details-post .blog-details-content .blog-quote .blockquote .blockquote-icon {
    flex-shrink: 0;
    height: 65px;
    width: 65px;
    line-height: 63px;
    background: #ffffff;
    box-shadow: 0px 9px 68px 0px rgba(0, 0, 0, 0.08);
    text-align: center;
    border-radius: 50%;
    position: relative;
    top: -90px;
    margin-bottom: -65px;
}

.blog-details-post .blog-details-content .blog-quote .blockquote .blockquote-icon svg {
    fill: #d02333;
    width: 30px;
}

.blog-details-post .blog-details-content .blog-quote .blockquote p {
    display: inline;
    font-size: 22px;
    font-family: "Outfit", sans-serif;
    font-weight: 400;
    color: #6f6d80;
    line-height: 32px;
    margin-top: 0;
}

.blog-details-post .blog-details-content .blog-quote .blockquote .name {
    font-size: 16px;
    line-height: 30px;
    color: #2f2a55;
    margin-top: 5px;
}

.blog-details-post .blog-details-content .blog-details-text {
    margin-top: 60px;
}

.blog-details-post .blog-details-content .blog-details-text .title {
    font-size: 24px;
    line-height: 36px;
    font-weight: 600;
    color: #2f2a55;
}

.blog-details-post .blog-details-content .blog-details-text p {
    font-size: 16px;
    line-height: 30px;
    color: #7a7a7a;
    margin-top: 5px;
}

.blog-details-post .blog-details-content .blog-details-tag-share {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    margin-top: 65px;
    padding-top: 15px;
    border-top: 1px solid #e3e3e5;
}

.blog-details-post .blog-details-content .blog-details-tag-share .blog-details-tag {
    padding-top: 20px;
}

.blog-details-post .blog-details-content .blog-details-tag-share .blog-details-tag .sidebar-widget {
    margin-top: 0;
    display: flex;
    align-items: center;
}

.blog-details-post .blog-details-content .blog-details-tag-share .blog-details-tag .sidebar-widget .label {
    font-size: 14px;
    line-height: 30px;
    font-family: "Outfit", sans-serif;
    font-weight: 400;
    color: #2f2a55;
    margin-right: 10px;
}

.blog-details-post .blog-details-content .blog-details-tag-share .blog-details-tag .sidebar-widget .sidebar-tag li {
    margin-bottom: 0px;
}

.blog-details-post .blog-details-content .blog-details-tag-share .blog-details-share {
    display: flex;
    align-items: center;
    padding-top: 20px;
}

.blog-details-post .blog-details-content .blog-details-tag-share .blog-details-share li {
    display: inline-block;
    margin-right: 10px;
}

.blog-details-post .blog-details-content .blog-details-tag-share .blog-details-share li:last-child {
    margin-right: 0;
}

.blog-details-post .blog-details-content .blog-details-tag-share .blog-details-share li a {
    display: inline-block;
    font-size: 13px;
    width: 35px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    color: #ffffff;
    border-radius: 50%;
}

.blog-details-post .blog-details-content .blog-details-tag-share .blog-details-share li a.share-facebook {
    background: #3452cf;
}

.blog-details-post .blog-details-content .blog-details-tag-share .blog-details-share li a.share-twitter {
    background: #39adf1;
}

.blog-details-post .blog-details-content .blog-details-tag-share .blog-details-share li a.share-pinterest {
    background: #d54443;
}

.blog-details-post .blog-details-content .upstudy-post-pagination {
    display: flex;
    flex-wrap: wrap;
    background: #ffffff;
    box-shadow: 0px 0px 70px 0px rgba(0, 0, 0, 0.16);
    padding: 10px 30px;
    margin-top: 80px;
    overflow: hidden;
    position: relative;
}

@media only screen and (max-width: 1199px) {
    .blog-details-post .blog-details-content .upstudy-post-pagination {
        padding: 10px 20px;
    }
}

@media only screen and (max-width: 575px) {
    .blog-details-post .blog-details-content .upstudy-post-pagination {
        padding: 0 10px;
    }
}

.blog-details-post .blog-details-content .upstudy-post-pagination::before {
    position: absolute;
    content: '';
    width: 1px;
    background-color: #e1e1e1;
    left: 50%;
    transform: translateX(-50%);
    top: 30px;
    bottom: 30px;
}

@media only screen and (max-width: 767px) {
    .blog-details-post .blog-details-content .upstudy-post-pagination::before {
        width: auto;
        height: 1px;
        left: 30px;
        right: 30px;
        top: 50%;
        transform: translateY(-50%);
        bottom: auto;
    }
}

.blog-details-post .blog-details-content .upstudy-post-pagination .previous-post,
.blog-details-post .blog-details-content .upstudy-post-pagination .next-post {
    width: 50%;
}

@media only screen and (max-width: 767px) {
    .blog-details-post .blog-details-content .upstudy-post-pagination .previous-post,
    .blog-details-post .blog-details-content .upstudy-post-pagination .next-post {
        width: 100%;
    }
}

.blog-details-post .blog-details-content .upstudy-post-pagination .blog-pagination-post {
    display: flex;
    align-items: center;
    padding: 30px 0;
}

@media only screen and (max-width: 1199px) {
    .blog-details-post .blog-details-content .upstudy-post-pagination .blog-pagination-post {
        padding: 20px 0;
    }
}

.blog-details-post .blog-details-content .upstudy-post-pagination .blog-pagination-post .post-thumb {
    flex-shrink: 0;
}

.blog-details-post .blog-details-content .upstudy-post-pagination .blog-pagination-post .post-thumb a img {
    border-radius: 50%;
    -o-object-position: center;
    object-position: center;
    -o-object-fit: cover;
    object-fit: cover;
}

.blog-details-post .blog-details-content .upstudy-post-pagination .blog-pagination-post .post-thumb a i {
    font-size: 14px;
    color: #d02333;
    padding: 0 10px;
}

.blog-details-post .blog-details-content .upstudy-post-pagination .blog-pagination-post .post-content {
    flex-grow: 1;
    padding: 0 20px;
}

@media only screen and (max-width: 1199px) {
    .blog-details-post .blog-details-content .upstudy-post-pagination .blog-pagination-post .post-content {
        padding: 0 15px;
    }
}

@media only screen and (max-width: 575px) {
    .blog-details-post .blog-details-content .upstudy-post-pagination .blog-pagination-post .post-content {
        padding: 0 13px;
    }
}

.blog-details-post .blog-details-content .upstudy-post-pagination .blog-pagination-post .post-content .title {
    font-size: 18px;
    line-height: 22px;
    font-weight: 600;
    color: #2f2a55;
}

@media only screen and (max-width: 1199px) {
    .blog-details-post .blog-details-content .upstudy-post-pagination .blog-pagination-post .post-content .title {
        font-size: 15px;
    }
}

.blog-details-post .blog-details-content .upstudy-post-pagination .blog-pagination-post .post-content .date {
    font-size: 12px;
    line-height: 22px;
    color: #838383;
}

.blog-details-post .blog-details-content .upstudy-post-pagination .blog-pagination-post .post-content .date i {
    color: #d02333;
    margin-right: 6px;
}

.blog-details-post .blog-details-content .comment-wrap .comment-form {
    margin-top: 50px;
}

.blog-details-post .blog-details-content .comment-wrap .comment-form .comment-title {
    font-size: 22px;
    line-height: 30px;
    color: #2f2a55;
}

.blog-details-post .blog-details-content .comment-wrap .comment-form p {
    font-size: 14px;
    line-height: 30px;
    color: #686f7a;
}

.blog-details-post .blog-details-content .comment-wrap .comment-form .comment-form-wrap {
    padding-top: 10px;
}

.blog-details-post .blog-details-content .comment-wrap .comment-form .comment-form-wrap .single-form {
    margin-top: 20px;
}

.blog-details-post .blog-details-content .comment-wrap .comment-form .comment-form-wrap .single-form .form-control {
    border: 1px solid transparent;
    background: #f6f7f9;
    border-radius: 0;
}

.blog-details-post .blog-details-content .comment-wrap .comment-form .comment-form-wrap .single-form .form-control::-webkit-input-placeholder {
    opacity: 0.95;
    font-size: 13px;
    font-family: "Outfit", sans-serif;
    color: #9d9d9d;
    font-weight: 400;
}

.blog-details-post .blog-details-content .comment-wrap .comment-form .comment-form-wrap .single-form .form-control:-moz-placeholder {
    opacity: 0.95;
    font-size: 13px;
    font-family: "Outfit", sans-serif;
    color: #9d9d9d;
    font-weight: 400;
}

.blog-details-post .blog-details-content .comment-wrap .comment-form .comment-form-wrap .single-form .form-control::-moz-placeholder {
    opacity: 0.95;
    font-size: 13px;
    font-family: "Outfit", sans-serif;
    color: #9d9d9d;
    font-weight: 400;
}

.blog-details-post .blog-details-content .comment-wrap .comment-form .comment-form-wrap .single-form .form-control:-ms-input-placeholder {
    opacity: 0.95;
    font-size: 13px;
    font-family: "Outfit", sans-serif;
    color: #9d9d9d;
    font-weight: 400;
}

.blog-details-post .blog-details-content .comment-wrap .comment-form .comment-form-wrap .single-form .form-control:focus {
    outline: none;
    border-color: #d02333;
}

.blog-details-post .blog-details-content .comment-wrap .comment-form .comment-form-wrap .single-form textarea.form-control {
    height: 135px;
    padding-top: 15px;
    font-size: 13px;
    color: #415674;
    font-weight: 600;
    padding: 10px 25px;
    resize: none;
}

.blog-details-post .blog-details-content .comment-wrap .comment-form .comment-form-wrap .single-form textarea.form-control:focus {
    outline: none;
    box-shadow: none;
}

.blog-details-post .blog-details-content .comment-wrap .comment-form .comment-form-wrap .form-btn .btn {
    height: 40px;
    line-height: 38px;
    box-shadow: 11px 10px 38px 0 rgba(46, 63, 99, 0.15);
    margin-top: 25px;
    font-size: 16px;
    padding: 0 45px;
}

/*--
/*  18 - Blog List CSS
/*----------------------------------------*/
.blog-standard-wrap {
    margin-top: -70px;
}

.blog-standard-wrap .blog-post-wrap .pagination {
    margin-top: 55px;
}

.single-blog-post {
    margin-top: 70px;
}

.single-blog-post .blog-img {
    border-radius: 10px;
    overflow: hidden;
}


.single-blog-post .blog-content {
    padding-top: 35px;
    padding-left: 0px;
    padding-right: 0px;
}

.single-blog-post .blog-content .blog-meta span i {
    color: #d02333;
}

.single-blog-post .blog-content .title {
    font-size: 30px;
    line-height: 40px;
}

.single-blog-post .blog-content p {
    font-size: 14px;
    line-height: 30px;
    color: #242424;
    margin-top: 20px;
    opacity: 1;
}

.single-blog-post .blog-content .blog-btn {
    margin-top: 25px;
}

.blog-sidebar {
    border: 1px solid #ebebeb;
    border-radius: 5px;
    padding: 30px;
    margin-top: 70px;
    margin-left: 65px;
    position: sticky;
    top: 160px;
}

@media only screen and (max-width: 1199px) {
    .blog-sidebar {
        margin-left: 15px;
    }
}

@media only screen and (max-width: 991px) {
    .blog-sidebar {
        margin-left: 0px;
    }
}

.blog-sidebar .sidebar-widget-1 {
    margin-top: 0;
}

.sidebar-widget {
    margin-top: 45px;
}

.sidebar-widget .search-form {
    position: relative;
}

.sidebar-widget .search-form input:not([type="submit"]):not([type="checkbox"]):not([type="radio"]):not([type="file"]) {
    width: 100%;
    height: 50px;
    margin-bottom: 0;
    border: 0;
    padding-right: 35px;
    background: #f2f2f2;
    color: #415674;
    font-weight: 700;
    outline: none;
    overflow: hidden;
    padding:0 30px;
    border-radius: 10px;
}

.sidebar-widget .search-form input:not([type="submit"]):not([type="checkbox"]):not([type="radio"]):not([type="file"])::-webkit-input-placeholder {
    opacity: 0.95;
    font-size: 14px;
    font-family: "Outfit", sans-serif;
    color: #898c94;
    font-weight: 400;
}

.sidebar-widget .search-form input:not([type="submit"]):not([type="checkbox"]):not([type="radio"]):not([type="file"]):-moz-placeholder {
    opacity: 0.95;
    font-size: 14px;
    font-family: "Outfit", sans-serif;
    color: #898c94;
    font-weight: 400;
}

.sidebar-widget .search-form input:not([type="submit"]):not([type="checkbox"]):not([type="radio"]):not([type="file"])::-moz-placeholder {
    opacity: 0.95;
    font-size: 14px;
    font-family: "Outfit", sans-serif;
    color: #898c94;
    font-weight: 400;
}

.sidebar-widget .search-form input:not([type="submit"]):not([type="checkbox"]):not([type="radio"]):not([type="file"]):-ms-input-placeholder {
    opacity: 0.95;
    font-size: 14px;
    font-family: "Outfit", sans-serif;
    color: #898c94;
    font-weight: 400;
}

.sidebar-widget .search-form button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 0;
    width: 50px;
    height: 50px;
    background: transparent;
    border: 0;
    border-radius: 5px;
    font-size: 14px;
    color: #111111;
    transition: all 0.3s linear;
}

.sidebar-widget .search-form button:hover {
    background: #d02333;
    color: #ffffff;
}

.sidebar-widget .widget-title {
    margin-bottom: 25px;
}

.sidebar-widget .widget-title .title {
    font-size: 18px;
    font-family: "Outfit", sans-serif;
    font-weight: 700;
    color: #2f2a55;
    display: inline-block;
}

.sidebar-widget .recent-posts ul li {
    margin-top: 30px;
}

.sidebar-widget .recent-posts ul li:first-child {
    margin-top: 0;
}

.sidebar-widget .recent-posts ul li .post-link {
    display: flex;
    align-items: center;
}

.sidebar-widget .recent-posts ul li .post-link .post-thumb {
    margin-right: 20px;
}

.sidebar-widget .recent-posts ul li .post-link .post-thumb img {
    width: 70px;
    height: 70px;
    border-radius: 6px;
    image-rendering: crisp-edges;
    object-fit: contain;
}

.title-blog {
    margin-bottom: 30px;
    font-size: 24px;
}

.sidebar-widget .recent-posts ul li .post-link .post-text .title {
    font-size: 14px;
    line-height: 16px;
    font-family: "Outfit", sans-serif;
    font-weight: 600;
    transition: all 0.3s linear;
    color: #333333;
}

.sidebar-widget .recent-posts ul li .post-link .post-text .post-meta {
    font-size: 12px;
    font-family: "Outfit", sans-serif;
    font-weight: 400;
    line-height: 24px;
    color: #838383;
    display: inline-block;
    margin-top: 5px;
}

.sidebar-widget .recent-posts ul li .post-link .post-text .post-meta i {
    color: #d02333;
    margin-right: 5px;
}

.sidebar-widget .recent-posts ul li .post-link:hover .post-text .title {
    color: #d02333;
}

.sidebar-widget .category .cate-item {
    margin-top: 15px;
}

.sidebar-widget .category .cate-item:first-child {
    margin-top: 0;
}

.sidebar-widget .category .cate-item a {
    display: flex;
    font-size: 14px;
    font-family: "Outfit", sans-serif;
    font-weight: 400;
    color: #29303c;
    transition: all 0.3s linear;
}

.sidebar-widget .category .cate-item a i {
    font-size: 10px;
    line-height: 24px;
    color: #d02333;
    margin-right: 5px;
}

.sidebar-widget .category .cate-item a .post-count {
    margin-left: auto;
    align-items: flex-start;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    border-radius: 50%;
    background: #e4f2f8;
    font-size: 14px;
    font-family: "Outfit", sans-serif;
    font-weight: 400;
    color: #d02333;
    transition: all 0.3s linear;
}

.sidebar-widget .category .cate-item a:hover {
    color: #d02333;
}

.sidebar-widget .category .cate-item a:hover .post-count {
    background: #d02333;
    color: #ffffff;
}

.sidebar-widget .sidebar-tag li {
    display: inline-block;
    margin-bottom: 10px;
    margin-right: 4px;
}

.sidebar-widget .sidebar-tag li a {
    display: inline-block;
    border-radius: 5px;
    padding: 8px 15px;
    font-size: 14px;
    font-family: "Outfit", sans-serif;
    font-weight: 400;
    color: #666666;
    line-height: 18px;
    background: #f9f9f9;
    transition: all 0.3s linear;
}

.sidebar-widget .sidebar-tag li a:hover {
    background-color: #d02333;
    color: #ffffff;
}

/*--Page Pagination--*/
.upstudy-pagination {
    margin-top: 60px;
}

.upstudy-pagination .pagination li a {
    width: 35px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    border-radius: 50%;
    font-size: 14px;
    transition: all 0.3s linear;
}

.upstudy-pagination .pagination li a.active {
    background-color: #d02333;
    color: #ffffff;

}

#context span {
    background: orange;
    color: black;
}


.image{
    width: 450px;
    height: 435px;
    overflow: hidden;
}

.about__left .image-2 {
    width: 400px;
    height: 350px;
    overflow: hidden;
}

.about__left .image-3{
    width: 360px;
    height: 530px;
    overflow: hidden;
}

.about__left .shape img {
    border:none;
}

.about__left img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-full {
    width: 80%;
    height: 650px;
    position: relative;
}


.video-btn {
    width: 200px;
    height: 150px;
    background: #d02333;
    position: absolute!important;
    bottom: 60px;
    z-index: 300;
    display: flex;
    justify-content: center;
    align-items: center;
    right: -60px;
    border-radius: 15px;
    overflow: hidden;

}

.video-btn .youtube-background {
    opacity: 0.4;
}

.video-btn svg {
    width: 60px;
    height: 60px;
}

.video-btn .video-button {
    border:none;
}
/*----------------------------------------*/
/*  00 - Section Title
/*----------------------------------------*/
.pbmit-heading-subheading{
    margin-bottom: 45px;
}
.pbmit-heading-subheading .pbmit-subtitle {
    font-family: var(--pbmit-heading-typography-font-family);
    font-weight: 600;
    font-size: 15px;
    line-height: 20px;
    letter-spacing: 2px;
    color: #d02333;
    text-transform: uppercase;
    font-weight: 600;
    font-style: normal;
    position: relative;
    display: inline-block;
    padding-left: 50px;
    margin-bottom: 8px;
}
.pbmit-heading-subheading .pbmit-subtitle::before{
    content: "";
    position: absolute;
    width: 40px;
    height: 1px;
    background-color: #d02333;
    top: 9px;
    left: 0;
}
.pbmit-heading-subheading .pbmit-title{
    font-family: var(--pbmit-heading-typography-font-family);
    font-size: 42px;
    line-height: 46px;
    letter-spacing: -.5px;
    color: #222d35;
    text-transform: none;
    font-weight: 600;
    font-style: normal;
}
.pbmit-heading-subheading.text-white .pbmit-title{
    color: var(--pbmit-white-color);
}
.pbmit-heading-subheading.text-global .pbmit-subtitle::before{
    background-color: var(--pbmit-white-color);
}
.pbmit-heading-subheading.text-global .pbmit-title ,
.pbmit-heading-subheading.text-global .pbmit-subtitle{
    color: var(--pbmit-white-color);
}
.pbmit-heading-subheading.text-white .pbmit-subtitle::before{
    background-color: #d02333;
}
.pbmit-heading-content{
    font-size: 18px;
    line-height: 28px;
    letter-spacing: 0;
}
.pbmit-heading-subheading.text-global .pbmit-heading-desc {
    color: var(--pbmit-white-color);
}
.pbmit-heading-subheading .pbmit-heading-desc {
    margin-top: 20px;
}

/*----------------------------------------*/
/*  01 - Icon Box
/*----------------------------------------*/
.pbmit-ihbox-icon-wrapper .pbmit-ihbox-svg-icon img {
    width: 55px;
    height: 55px;
    margin-bottom: 15px;
}
/** Style 1 **/
.pbmit-ihbox-style-1{
    position: relative;
    padding: 40px;
    margin-bottom: 0px;
    background-color: #d02333;
}
.pbmit-ihbox-style-1.pbmit-ihbox h2{
    font-size: 20px;
    line-height: 24px;
    margin-bottom: 0;
    color: #fff;
}
.pbmit-ihbox-style-1 .pbmit-ihbox-icon-wrapper{
    font-size: 50px;
    line-height: 50px;
    margin-right: 20px;
    color: #fff;
}
/** Style 2 **/
.pbmit-ihbox-style-2{
    position: relative;
    margin-bottom: 30px;
}
.pbmit-ihbox-style-2 .pbmit-ihbox-box-number{
    position: absolute;
    bottom: -26px;
    right: 0;
    font-size: 48px;
    line-height: 48px;
    color: rgb(121 121 121 / 20%);
    display: inline-block;
    padding-right: 0px;
    background: #fff;
    font-weight: 700 !important;
    font-family: Rajdhani,sans-serif;
}
.pbmit-ihbox-style-2 .pbmit-ihbox-headingicon{
    border-bottom: 1px solid #e2e3e4;
    position: relative;
    margin-bottom: 60px;
}
.pbmit-ihbox-style-2.pbmit-ihbox h2{
    font-size: 24px;
    line-height: 26px;
    margin-bottom: 10px;
}
.pbmit-ihbox-style-2.pbmit-ihbox h4{
    font-size: 13px;
    line-height: 20px;
    margin-bottom: 5px;
    text-transform: uppercase;
    margin-bottom: 0;
    letter-spacing: 1.5px;
}
.pbmit-ihbox-style-2 .pbmit-ihbox-icon-wrapper{
    font-size: 70px;
    line-height: 70px;
    margin-bottom: 25px;
}
.pbmit-ihbox-style-2 .pbmit-heading-desc{
    margin-top: 0;
    padding-right: 10px;
}
.pbmit-ihbox-style-2 .pbmit-ihbox-btn{
    margin-top: 15px;
}
.pbmit-ihbox-style-2 .pbmit-ihbox-icon-wrapper i{
    color: #d02333;
}


.about-section-one {
    overflow: hidden;
}
.about-one-img {
    position: relative;
    height: 100%;
    padding: 70px 60px 100px 0px;
}
.about-one-img::before{
    position: absolute;
    height: 50%;
    width: 164%;
    top: 0;
    right: 0;
    content: "";
    display: block;
    z-index: -1;
    margin-right: 210px;
    background-color: #dedede;
    display: none;
}


.about-one-img img {
    border-radius:15px;
    border:5px solid #dedede;
}


.pbmit-blc-style-1 .about-us-button{
    display: flex;
    align-items: center;
    margin-top: 30px;
}

.pbmit-stretched-div{
    position: relative;
    margin: 0px 210px 0px 0px;
    background-color: var(--pbmit-light-color);
}
.pbmit-col-stretched-yes .pbmit-stretched-div {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    width: auto;
    z-index: 1;
    overflow: hidden;
}
.pbmit-stretched-div:before{
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    content: "";
    display: block;
    z-index: 1;
}
blockquote {
    font-weight: 700;
    font-weight: 700;
    font-style: normal;
}
blockquote p {
    margin-bottom: 0;
    position: relative;
    z-index: 1;
}
blockquote cite, blockquote small {
    display: block;
    font-size: 10px;
    line-height: 23px;
    font-style: normal!important;
    font-weight: 600!important;
    margin-top: 15px;
    letter-spacing: 1px;
    text-transform: uppercase;
    opacity: 1;
}
.wp-block-quote.is-style-large cite em:before,
blockquote cite:before,
blockquote small:before {
    content: "\2014\00a0";
}
.pbmit-blc-style-1 {
    padding: 86px 0px 0px 20px;
}
.pbmit-blc-style-1 blockquote {
    margin: 0;
    border: 0;
    padding: 0;
    font-weight: normal!important;
    position: relative;
    padding-left: 45px;
    font-size: 24px;
    line-height: 36px;
}
.pbmit-blc-style-1 blockquote:before {
    position: absolute;
    content: '"';
    font-size: 60px;
    line-height: 60px;
    font-weight: 700;
    top: -28px;
    left: 0;
    transform: rotate(180deg);
    color: #d02333;
}
.inbox-button1{
    margin-top: 0px;
    text-align: end;
}
.g-0, .gx-0 {
    --bs-gutter-x: 0;
}
.pbmit-bg-color-light4{background-color: #f6f6f6;}.pbmit-heading-subheading{margin-bottom: 45px;}.pbmit-heading-subheading .pbmit-subtitle {font-family: var(--pbmit-heading-typography-font-family);font-weight: 600;font-size: 15px;line-height: 20px;letter-spacing: 2px;color: #d02333;text-transform: uppercase;font-weight: 600;font-style: normal;position: relative;display: inline-block;padding-left: 50px;margin-bottom: 8px;}.pbmit-heading-subheading .pbmit-subtitle::before{content: "";position: absolute;width: 40px;height: 1px;background-color: #d02333;top: 9px;left: 0;}.pbmit-heading-subheading .pbmit-title{font-family: var(--pbmit-heading-typography-font-family);font-size: 42px;line-height: 46px;letter-spacing: -.5px;color: #222d35;text-transform: none;font-weight: 600;font-style: normal;}.pbmit-heading-subheading.text-white .pbmit-title{color: var(--pbmit-white-color);}.pbmit-heading-subheading.text-global .pbmit-subtitle::before{background-color: var(--pbmit-white-color);}.pbmit-heading-subheading.text-global .pbmit-title , .pbmit-heading-subheading.text-global .pbmit-subtitle{color: var(--pbmit-white-color);}.pbmit-heading-subheading.text-white .pbmit-subtitle::before{background-color: #d02333;}.pbmit-heading-content{font-size: 18px;line-height: 28px;letter-spacing: 0;}.pbmit-heading-subheading.text-global .pbmit-heading-desc {color: var(--pbmit-white-color);}.pbmit-heading-subheading .pbmit-heading-desc {margin-top: 20px;}.pbmit-ihbox-icon-wrapper .pbmit-ihbox-svg-icon img {width: 55px;height: 55px;margin-bottom: 15px;}.pbmit-ihbox-style-1{position: relative;padding: 40px;margin-bottom: 0px;background-color: #d02333;}.pbmit-ihbox-style-1.pbmit-ihbox h2{font-size: 20px;line-height: 24px;margin-bottom: 0;color: #fff;}.pbmit-ihbox-style-1 .pbmit-ihbox-icon-wrapper{font-size: 50px;line-height: 50px;margin-right: 20px;color: #fff;}.pbmit-ihbox-style-2{position: relative;margin-bottom: 30px;}.pbmit-ihbox-style-2 .pbmit-ihbox-box-number{position: absolute;bottom: -26px;right: 0;font-size: 48px;line-height: 48px;color: rgb(121 121 121 / 20%);display: inline-block;padding-right: 0px;background: #fff;font-weight: 700 !important;font-family: Rajdhani,sans-serif;}.pbmit-ihbox-style-2 .pbmit-ihbox-headingicon{border-bottom: 1px solid #e2e3e4;position: relative;margin-bottom: 60px;}.pbmit-ihbox-style-2.pbmit-ihbox h2{font-size: 24px;line-height: 26px;margin-bottom: 10px;}.pbmit-ihbox-style-2.pbmit-ihbox h4{font-size: 13px;line-height: 20px;margin-bottom: 5px;text-transform: uppercase;margin-bottom: 0;letter-spacing: 1.5px;}.pbmit-ihbox-style-2 .pbmit-ihbox-icon-wrapper{font-size: 70px;line-height: 70px;margin-bottom: 25px;}.pbmit-ihbox-style-2 .pbmit-heading-desc{margin-top: 0;padding-right: 10px;}.pbmit-ihbox-style-2 .pbmit-ihbox-btn{margin-top: 15px;}.pbmit-ihbox-style-2 .pbmit-ihbox-icon-wrapper i{color: #d02333;}.pbmit-ihbox-style-3 .pbmit-heading-desc ul li:before, .pbmit-ihbox-style-3 .pbmit-ihbox-icon-wrapper i, .pbmit-ihbox-style-3 .pbmit-ihbox-box-number i, .pbmit-ihbox-style-3{color: #d02333;-webkit-transition: all .25s ease-in-out;transition: all .25s ease-in-out;}.pbmit-ihbox-style-3{position: relative;margin-bottom: 0px;border: 1px solid #e2e3e4;border-right: 0;padding: 30px 40px;}.pbmit-ihbox-style-3 .pbmit-ihbox-box-number{font-size: 24px;line-height: 24px;color: #e8e8e8;position: absolute;right: 10px;top: 10px;}.pbmit-ihbox-style-3.pbmit-ihbox h2{font-size:24px;line-height: 28px;margin-bottom: 0;}.pbmit-ihbox-style-3 .pbmit-ihbox-content{margin-bottom: 15px;margin-top: 10px;}.pbmit-ihbox-style-3 .pbmit-ihbox-icon-wrapper{font-size: 55px;line-height: 55px;margin-bottom: 20px;}.pbmit-ihbox-style-3 .pbmit-heading-desc{margin: 20px 0;}.pbmit-ihbox-style-3 .pbmit-heading-desc ul{margin: 0;padding: 0;list-style: none;}.pbmit-ihbox-style-3 .pbmit-heading-desc ul li{padding: 3px 0;color: #606060;}.pbmit-ihbox-style-3 .pbmit-heading-desc ul li:before {content: '' !important;display: inline-block;width: 5px;height: 5px;background-color: #d02333;margin-right: 15px;}.pbmit-ihbox-style-3 .pbmit-ihbox-btn i{font-weight: bold;font-size: 24px;}.pbmit-ihbox-style-3:hover{background-color: var(--pbmit-secondary-color);}.pbmit-ihbox-style-3:hover .pbmit-heading-desc ul li:before{background-color: #fff;}.pbmit-ihbox-style-3:hover .pbmit-ihbox-btn i, .pbmit-ihbox-style-3:hover h2, .pbmit-ihbox-style-3:hover .pbmit-ihbox-icon-wrapper i, .pbmit-ihbox-style-3:hover .pbmit-heading-desc ul li, .pbmit-ihbox-style-3:hover .pbmit-ihbox-box-number i{color: #fff;}.pbmit-miconheading-style-3:last-child .pbmit-ihbox-style-3{border-right: 1px solid #e2e3e4;}.pbmit-miconheading-style-3 .pbmit-blog-style-3{margin-bottom: 30px;}.pbmit-ihbox-style-4{background-color: #071323;padding: 30px 30px;}.pbmit-ihbox-style-4 .pbmit-element-title{font-size: 16px;line-height: 20px;margin-bottom: 0;}.pbmit-ihbox-style-4 .pbmit-ihbox-icon-wrapper{font-size: 65px;line-height: 65px;margin-bottom: 25px;}.pbmit-ihbox-style-5 .pbmit-element-title{font-size: 24px;line-height: 28px;margin-bottom: 0px;}.pbmit-ihbox-style-5 .pbmit-ihbox-icon-wrapper{font-size: 24px;line-height: 24px;margin-right: 10px;}.pbmit-ihbox-style-5 .pbmit-ihbox-contents{padding-top: 10px;}.pbmit-ihbox-style-4 .pbmit-ihbox-icon-wrapper i, .pbmit-ihbox-style-5 .pbmit-ihbox-icon-wrapper i{color: #d02333;}.pbmit-ihbox-style-6 .pbmit-ihbox-box-number, .pbmit-ihbox-style-6 .pbmit-element-title{font-size: 24px;line-height: 28px;margin-bottom: 0;font-family: Rajdhani,sans-serif;}.pbmit-ihbox-style-6 .pbmit-ihbox-box-number{font-family: Rajdhani,sans-serif;padding-right: 5px;font-weight: 600;color: #d02333;}.pbmit-ihbox.pbmit-ihbox-style-6 {margin-bottom: 30px;}.pbmit-ihbox-style-7 .pbmit-element-title{font-size: 24px;line-height: 28px;margin-bottom: 5px;}.pbmit-ihbox-style-7 .pbmit-ihbox-icon-wrapper{font-size:60px;line-height: 60px;margin-right: 25px;margin-top: 3px;color: #d02333;}.pbmit-team-style-1.pbmit-ele-team{position: relative;}.pbmit-team-style-1, .pbmit-team-style-1 .pbminfotech-box-social-links, .pbmit-team-style-1 .pbminfotech-box-content{-webkit-transition: all 0.5s cubic-bezier(0.645, 0.045, 0.355, 1);;-o-transition: all 0.5s cubic-bezier(0.645, 0.045, 0.355, 1);;transition: all 0.5s cubic-bezier(0.645, 0.045, 0.355, 1);;}.pbmit-team-style-1 .pbminfotech-box-content .pbmit-team-title{font-size: 24px;line-height: 28px;margin-bottom: 0px;}.pbmit-team-style-1 .pbminfotech-box-content .pbmit-team-title, .pbmit-team-style-1 .pbminfotech-box-content .pbmit-team-title a{color: #fff;}.pbmit-team-style-1 .pbminfotech-box-team-position{text-transform: uppercase;letter-spacing: 2px;font-size: 14px;margin-bottom: 0;font-weight: 700;color: #d02333;}.pbmit-team-style-1 .pbminfotech-box-social-links {position: absolute;top: 40px;width: 50px;right: 40px;visibility: hidden;opacity: 0;}.pbmit-team-style-1 .pbminfotech-box-social-links, .pbmit-team-style-1 .pbmit-team-social-links li a{-webkit-transition: all 0.5s cubic-bezier(0.645, 0.045, 0.355, 1);-o-transition: all 0.5s cubic-bezier(0.645, 0.045, 0.355, 1);transition: all 0.5s cubic-bezier(0.645, 0.045, 0.355, 1);}.pbmit-team-style-1:hover .pbminfotech-box-social-links{visibility: visible;opacity: 1;}.pbmit-team-style-1 .pbmit-team-social-links li {margin-top: 5px;}.pbmit-team-style-1 .pbmit-team-social-links li a {color: #fff;width: 50px;height: 50px;line-height: 50px;text-align: center;padding: 0;display: block;font-size: 15px;background-color: #d02333;}.pbmit-team-style-1 .pbmit-team-social-links li a:hover{background: #fff !important;color: #222631;}.pbmit-team-style-1 .pbminfotech-box-content {position: absolute;width: calc(100% - 0px);top: 0;height: 100%;z-index: 1;}.pbmit-team-style-1 .pbminfotech-post-item{position: relative;overflow: hidden;}.pbmit-team-style-1 .pbminfotech-post-item:after{position: absolute;top: 0;left: 0x;right: 0;background: -moz-linear-gradient(90deg, rgba(32,36,38,0.72) 0%, rgba(32,36,38,0.72) 14%, rgba(32,36,38,0) 100%);background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(32,36,38,0)), color-stop(86%, rgba(32,36,38,0.72)), color-stop(100%, rgba(32,36,38,0.72)));background: -webkit-linear-gradient(90deg, rgba(32,36,38,0.72) 0%, rgba(32,36,38,0.72) 14%, rgba(32,36,38,0) 100%);background: -o-linear-gradient(90deg, rgba(32,36,38,0.72) 0%, rgba(32,36,38,0.72) 14%, rgba(32,36,38,0) 100%);background: -ms-linear-gradient(90deg, rgba(32,36,38,0.72) 0%, rgba(32,36,38,0.72) 14%, rgba(32,36,38,0) 100%);background: linear-gradient(0deg, rgba(32,36,38,0.72) 0%, rgba(32,36,38,0.72) 14%, rgba(32,36,38,0) 100%);overflow: hidden;width: 100%;height: 100%;content: '';opacity: 0;transition: all 0.4s ease-out;-moz-transition: all 0.4s ease-out;-webkit-transition: all 0.4s ease-out;-o-transition: all 0.4s ease-out;}.pbmit-team-style-1:hover .pbminfotech-post-item:after {opacity: 1;}.pbmit-team-style-1 .pbminfotech-box-content-inner{position: absolute;left: 30px;transition: .3s cubic-bezier(.43,.07,.61,.95);bottom: 40px;transform: translateY(100%);opacity: 0;}.pbmit-team-style-1:hover .pbminfotech-box-content-inner{opacity: 1;transform: translateY(0);}.pbmit-team-style-2 {margin-bottom: 30px;}.pbmit-team-style-2 .pbminfotech-team-image-box{margin-bottom: 20px;position: relative;overflow: hidden;}.pbmit-team-style-2 .pbminfotech-team-image-box img{width: 100%;}.pbmit-team-style-2 .pbminfotech-box-content{text-align: center;}.pbmit-team-style-2 .pbminfotech-box-content .pbmit-team-title{margin-bottom: 0;font-size: 24px;line-height: 28px;}.pbmit-team-style-2 .pbminfotech-box-team-position {text-transform: uppercase;letter-spacing: 2px;font-size: 14px;margin-bottom: 0;font-weight: 600;color: #d02333;}.pbmit-team-style-2 .pbminfotech-team-image-box:before {content: "";position: absolute;right: 0;top: 0;width: 0;height: 100%;transition: .5s;opacity: .57;}.pbmit-team-style-2:hover .pbminfotech-team-image-box:before{width: 100%;}.pbmit-team-style-2 .pbminfotech-box-social-links ul {position: absolute;right: -50px;top: 0;height: 100%;width: 50px;text-align: center;box-shadow: 0px 5px 12.09px 0.91px rgba(133, 127, 151, 0.11);padding: 30px 0;transition: .5s;display: flex;flex-direction: column;align-items: center;justify-content: center;margin: 0;background: #d02333;}.pbmit-team-style-2:hover .pbminfotech-box-social-links ul{right: 0;}.pbmit-team-style-2 .pbmit-team-social-links li {display: inline-block;margin-right: 0px;margin-top: 14px;}.pbmit-team-style-2 .pbminfotech-box-social-links a{font-size: 16px;color: #fff;}.pbmit-digit-box{font-size: 17px;line-height: normal;display: flex;margin-top: 40px;z-index: 1;position: relative;}.pbmit-digit-box .pbmit-digit{font-size: 40px;font-weight: 700 !important;margin-right: 25px;font-family: Rajdhani,sans-serif;color: #d02333;}.pbmit-digit-box .pbmit-digit sup{font-size: 26px;line-height: 26px;font-weight: bold;}.pbminfotech-ele-fid-style-1{padding: 35px;position: relative;background: #fff;padding-top: 45px;}.pbminfotech-ele-fid-style-1 .pbmit-sbox-icon-wrapper{position: absolute;top: 30px;right: 35px;font-size: 80px;line-height: 80px;opacity: 0.2;}.pbminfotech-ele-fid-style-1 .pbmit-fid-title{font-size: 18px;line-height: 28px;}.pbminfotech-ele-fid-style-1 .pbmit-fid-inner{font-size: 76px;line-height: 76px;margin-bottom: 5px;}.pbminfotech-ele-fid-style-1 .pbmit-fid sup {font-size: 40px;line-height: 40px;top: -28px;left: -16px;color: #d02333;}.pbmit-fld-active.pbminfotech-ele-fid-style-1 .pbmit-sbox-icon-wrapper i, .pbmit-fld-active.pbminfotech-ele-fid-style-1 .pbmit-fid-inner, .pbmit-fld-active.pbminfotech-ele-fid-style-1{color: #fff;}.pbmit-fld-active.pbminfotech-ele-fid-style-1 .pbmit-fid span{color: #222631;position: relative;left: -16px;}.pbminfotech-ele-fid-style-1 .pbmit-sbox-icon-wrapper i{color: #d02333;}.pbminfotech-ele-fid-style-2{text-align: center;}.pbminfotech-ele-fid-style-2 .pbmit-circle {width: 125px;margin: 0 auto;position: relative;}.pbminfotech-ele-fid-style-2 .pbmit-fid-inner{font-size: 30px;line-height: 34px;font-weight: 600;margin-bottom: 0;position: relative;display: inline-block;position: absolute;top: 50%;-khtml-transform: translateX(0%) translateY(-50%);-moz-transform: translateX(0%) translateY(-50%);-ms-transform: translateX(0%) translateY(-50%);-o-transform: translateX(0%) translateY(-50%);transform: translateX(0%) translateY(-50%);left: 0;text-align: center;width: 100%;}.pbminfotech-ele-fid-style-2 .pbmit-fid-title {font-size: 18px;line-height: 26px;margin-bottom: 0;margin-top: 5px;letter-spacing: 0;}.pbminfotech-ele-fid-style-2 .pbmit-fid-sub{font-size: 20px;line-height: 26px;}.pbminfotech-ele-fid-style-2 .pbmit-fid-sub sup{top: 0;line-height: normal;font-size: inherit;vertical-align: top;}.text-white .pbminfotech-ele-fid-style-2 .pbmit-fid-inner , .text-white .pbminfotech-ele-fid-style-2 .pbmit-fid-title{color: var(--pbmit-white-color);}.pbminfotech-ele-fid-style-3{text-align: left;}.pbminfotech-ele-fid-style-3 .pbmit-circle {width: 125px;margin-bottom: 20px;text-align: center;position: relative;}.pbminfotech-ele-fid-style-3 .pbmit-fid-inner{font-size: 40px;line-height: 44px;font-weight: normal;color: #fff;margin-bottom: 0;position: relative;display: inline-block;position: absolute;top: 50%;-khtml-transform: translateX(0%) translateY(-50%);-moz-transform: translateX(0%) translateY(-50%);-ms-transform: translateX(0%) translateY(-50%);-o-transform: translateX(0%) translateY(-50%);transform: translateX(0%) translateY(-50%);left: 0;text-align: center;width: 100%;}.pbminfotech-ele-fid-style-3 .pbmit-fid-title {font-size: 24px;line-height: 26px;margin-bottom: 0;margin-top: 5px;color: #fff;}.pbminfotech-ele-fid-style-3 .pbmit-fid-sub sup{top: 0;line-height: normal;font-size: inherit;vertical-align: top;}.pbmit-service-cat a{color: #d02333;}.pbmit-service-style-1 .pbmit-featured-wrapper img, .pbmit-service-style-1 .pbminfotech-post-item{-webkit-transition: all 0.5s ease 0s;-o-transition: all 0.5s ease 0s;transition: all 0.5s ease 0s;}.pbmit-service-style-1 .pbminfotech-post-item{border: 1px solid #eee;padding: 0 30px 10px;margin-top: 45px;}.pbmit-service-style-1:hover .pbminfotech-post-item{border-color:#d02333 ;}.pbmit-service-style-1 .pbmit-service-img-wrapper{margin-top: -45px;position: relative;margin-bottom: 30px;overflow: hidden;}.pbmit-service-style-1:hover .pbmit-featured-wrapper img{-webkit-transform: scale(1.2);-ms-transform: scale(1.2);transform: scale(1.2);}.pbmit-service-style-1 .pbmit-service-title {position: relative;font-size: 24px;line-height: 26px;margin: 0px 0 15px;}.pbmit-service-style-1 .pbmit-service-icon-wrapper{display: inline-block;height: 60px;width: 60px;line-height: 60px;font-size: 40px;text-align: center;color: #fff;position: absolute;right: 0;bottom: 0px;background-color: #d02333;-webkit-transition: all .25s ease-in-out;transition: all .25s ease-in-out;}.pbmit-service-style-1 .pbmit-service-cat:before{background-color: #d02333;}.pbmit-service-style-1 {margin-bottom: 30px;}.pbmit-service-style-2 .pbmit-featured-wrapper img, .pbmit-service-style-2 .pbminfotech-post-item{-webkit-transition: all 0.5s ease 0s;-o-transition: all 0.5s ease 0s;transition: all 0.5s ease 0s;}.pbmit-service-style-2 .pbminfotech-post-item{border: 1px solid #eee;padding: 35px 30px 0;margin-bottom: 30px;}.pbmit-service-style-2:hover .pbminfotech-post-item{border-color: #d02333;}.pbmit-service-style-2 .pbmit-featured-wrapper{margin-bottom: -30px;overflow: hidden;}.pbmit-service-style-2 .pbmit-featured-wrapper img {position: relative;width: 100%;}.pbmit-service-style-2:hover .pbmit-featured-wrapper img{-webkit-transform: scale(1.2);-ms-transform: scale(1.2);transform: scale(1.2);}.pbmit-service-style-2 .pbmit-service-title {position: relative;font-size: 24px;line-height: 26px;margin: 0px 0 20px;}.pbmit-service-style-2 .pbmit-service-icon-wrapper{line-height: 50px;font-size: 50px;text-align: right;}.pbmit-service-style-2 .pbmit-service-cat:before{background-color: #d02333;}.pbmit-service-style-2 .pbmit-service-icon-wrapper i{color: #d02333;}.pbmit-service-style-3 .pbmit-service-cat:before, .pbmit-service-style-3 .pbmit-service-cat a, .pbmit-service-style-3 .pbmit-service-title a, .pbmit-service-style-3 .pbmit-featured-wrapper img, .pbmit-service-style-3 .pbminfotech-box-content, .pbmit-service-style-3 .pbmit-service-title{-webkit-transition: all 0.5s ease 0s;-o-transition: all 0.5s ease 0s;transition: all 0.5s ease 0s;}.pbmit-service-style-3 .pbmit-service-cat a{color: #d02333;}.pbmit-service-style-3 .pbmit-service-cat:before {background-color: #d02333;}.pbmit-service-style-3 .pbmit-service-title {position: relative;font-size: 24px;line-height: 26px;margin: 0px 0 20px;}.pbmit-service-style-3 .pbmit-featured-wrapper{position: relative;display: block;overflow: hidden;}.pbmit-service-style-3 .pbmit-featured-wrapper img {position: relative;width: 100%;}.pbmit-service-style-3:hover .pbmit-featured-wrapper img{-webkit-transform: scale(1.2);-ms-transform: scale(1.2);transform: scale(1.2);}.pbmit-service-style-3 .pbminfotech-box-content {padding: 25px 30px 5px;margin: -50px 30px 0;background-color: #fff;position: relative;}.pbmit-service-style-3:hover .pbminfotech-box-content{background-color: #d02333;}.pbmit-service-style-3:hover .pbmit-service-cat:before{background-color: #fff;}.pbmit-service-style-3:hover .pbmit-service-cat a, .pbmit-service-style-3:hover .pbmit-service-title a{color: #fff;}.pbmit-port-cat, .pbmit-service-cat{letter-spacing: 2px;font-size: 14px;position: relative;padding-left: 45px;font-weight: 600;font-family: Rajdhani,sans-serif;}.pbmit-port-cat:before, .pbmit-service-cat:before {position: absolute;left: 0;top: 44%;content: '';width: 30px;height: 1px;background-color: #000;}.pbmit-port-cat a{color: #d02333;}.pbminfotech-overlay-box:hover img {transform: scale(1, 1);transition: all 0.2s ease 0s;-webkit-transition: all 0.2s ease 0s;-moz-transition: all 0.2s ease 0s;-ms-transition: all 0.2s ease 0s;-o-transition: all 0.2s ease 0s;filter: blur(4px);-webkit-filter: blur(1px);-moz-filter: blur(1px);-ms-filter: blur(1px);-o-filter: blur(1px);}.pbmit-portfolio-style-1 .pbminfotech-post-content{position: relative;overflow: hidden;}.pbmit-portfolio-style-1 img {width: 100%;height: auto;transform: scale(1);-moz-transform: scale(1);-ms-transform: scale(1);-webkit-transform: scale(1);-o-transform: scale(1);transition: all ease 400ms;-moz-transition: all ease 400ms;-webkit-transition: all ease 400ms;}.pbmit-portfolio-style-1:hover img {transform: scale(1.1);-moz-transform: scale(1.1);-webkit-transform: scale(1.1);-ms-transform: scale(1.1);-o-transform: scale(1.1);}.pbmit-portfolio-style-1 .pbminfotech-box-content{visibility: hidden;opacity: 0;position: absolute;left: 20px;bottom: 50px;z-index: 3;transition: all ease 900ms;-moz-transition: all ease 900ms;-webkit-transition: all ease 900ms;-o-transition: all ease 900ms;}.pbmit-portfolio-style-1 .pbminfotech-box-content .pbmit-portfolio-title{margin-bottom: 0;font-size: 22px;text-transform: uppercase;}.pbmit-portfolio-style-1:hover .pbmit-port-cat:before{background-color: #fff;}.pbmit-portfolio-style-1 .pbminfotech-box-content .pbmit-port-cat a{font-weight: 500;}.pbmit-portfolio-style-1 .pbminfotech-box-content .pbmit-port-cat a, .pbmit-portfolio-style-1 .pbminfotech-box-content .pbmit-portfolio-title a{color: #fff;}.pbmit-portfolio-style-1:hover .pbminfotech-box-content {visibility: visible;opacity: 1;left: 50px;}.pbmit-portfolio-style-1 .pbminfotech-post-content:after {position: absolute;bottom: 0;left: 0;right: 0;background: rgba(230, 43, 74, .8);overflow: hidden;width: 0;height: 100%;content: '';transition: all 0.4s ease-out;-moz-transition: all 0.4s ease-out;-webkit-transition: all 0.4s ease-out;-o-transition: all 0.4s ease-out;}.pbmit-portfolio-style-1 .pbminfotech-post-content:hover:after {width: 100%;}.pbmit-portfolio-style-1 .pbminfotech-icon-box a{color: #fff;}.pbmit-portfolio-style-1 .pbminfotech-icon-box {width: 37px;height: 37px;position: absolute;right: 15px;top: 10px;font-size: 30px;line-height: 30px;line-height: .8;color: #fff;display: inline-block;text-align: center;z-index: 2;padding: 11px 17px;visibility: hidden;opacity: 0;transition: all ease 800ms;-moz-transition: all ease 800ms;-webkit-transition: all ease 800ms;}.pbmit-portfolio-style-1:hover .pbminfotech-icon-box {visibility: visible;opacity: 1;right: 40px;}.pbmit-portfolio-style-1.col-md-4 .pbminfotech-box-content{left: -20px;bottom: 20px;}.pbmit-portfolio-style-1.col-md-4:hover .pbminfotech-box-content {left: 20px;}.pbmit-portfolio-style-2 {margin-bottom: 30px;}.pbmit-portfolio-style-2 .pbminfotech-post-item {position: relative;}.pbmit-portfolio-style-2 .pbmit-content-wrapper {position: absolute;width: calc(100% - 70px);bottom: 0;background: #fff;margin: 0 30px;padding: 20px 40px 0 30px;opacity: 0;overflow: hidden;}.pbmit-portfolio-style-2 h3.pbmit-portfolio-title {font-size: 24px;line-height: 28px;}.pbmit-portfolio-style-2 .pbmit-content-wrapper, .pbmit-portfolio-style-2 .pbmit-link-icon{-webkit-transition: all .25s ease-in-out;transition: all .25s ease-in-out;}.pbmit-portfolio-style-2:hover .pbmit-content-wrapper {opacity: 1;bottom: 30px;}.pbmit-port-cat:before, .pbmit-service-cat:before{background-color: #d02333;}.pbmit-meta-line {margin-bottom: 15px;display: inline-block;margin-right: 10px;}.pbmit-meta-line i{color: #d02333;}.pbmit-meta-line a, .pbmit-meta-line {color: #606060;font-size: 14px;font-weight: 500!important;text-transform: uppercase;letter-spacing: 1px;font-family: Rajdhani,sans-serif;}.pbmit-blog-style-1 {margin-bottom: 30px;}.pbmit-blog-style-1 .post-item{border: 1px solid #eee;padding: 20px 20px 0;}.pbmit-blog-style-1 .pbminfotech-box-content{padding: 25px 10px;position: relative;}.pbmit-blog-style-1 .pbminfotech-box-content .pbmit-post-title{font-size: 26px;line-height: 30px;margin-bottom: 15px;}.pbmit-blog-style-1 .pbminfotech-box-desc{margin-top: 10px;}.pbmit-blog-style-1 .pbmit-meta-date-wrapper{position: absolute;right: 20px;top: -40px;padding: 8px 15px;text-align: center;background-color: #d02333;}.pbmit-blog-style-1 .pbmit-meta-date-wrapper span{display: block;color: #fff;text-transform: uppercase;font-family: Rajdhani,sans-serif;line-height: 36px;font-size: 18px;line-height: 18px;font-weight: 600 !important;letter-spacing: 1.5px;}.pbmit-blog-style-1 .pbmit-meta-date-wrapper span.pbmit-day{font-size: 34px;line-height: 34px;font-weight: 700 !important;}.pbmit-blog-style-1 .pbmit-featured-wrapper {position: relative;display: block;overflow: hidden;}.pbmit-blog-style-1 .pbmit-featured-wrapper img {position: relative;width: 100%;-webkit-transition: all 0.5s ease 0s;-o-transition: all 0.5s ease 0s;transition: all 0.5s ease 0s;}.pbmit-blog-style-1:hover .pbmit-featured-wrapper img {-webkit-transform: scale(1.2);-ms-transform: scale(1.2);transform: scale(1.2);}.pbmit-blog-style-2 .pbmit-read-more-link a, .pbmit-blog-style-1 .pbmit-read-more-link a {font-size: 14px;line-height: 23px;font-weight: 600;text-transform: uppercase;font-family: Rajdhani,sans-serif;color: #d02333;}.pbmit-ihbox-style-4 .pbmit-ihbox-btn a span, .pbmit-ihbox-style-2 .pbmit-ihbox-btn a span, .pbmit-service-style-3 .pbmit-service-btn-a span, .pbmit-service-style-2 .pbmit-service-btn-a span, .pbmit-blog-classic-inner .pbmit-read-more-link a span, .pbmit-blog-style-2 .pbmit-read-more-link a span, .pbmit-blog-style-1 .pbmit-read-more-link a span {position: relative;display: inline-block;line-height: normal;padding-left: 30px;}.pbmit-ihbox-style-4 .pbmit-ihbox-btn a span:after, .pbmit-ihbox-style-2 .pbmit-ihbox-btn a span:after, .pbmit-service-style-3 .pbmit-service-btn-a span:after, .pbmit-service-style-2 .pbmit-service-btn-a span:after, .pbmit-blog-classic-inner .pbmit-read-more-link a span:after, .pbmit-blog-style-2 .pbmit-read-more-link a span:after, .pbmit-blog-style-1 .pbmit-read-more-link a span:after {position: relative;display: inline-block;line-height: normal;}.pbmit-ihbox-style-4 .pbmit-ihbox-btn a span:after, .pbmit-ihbox-style-2 .pbmit-ihbox-btn a span:after, .pbmit-service-style-3 .pbmit-service-btn-a span:after, .pbmit-service-style-2 .pbmit-service-btn-a span:after, .pbmit-blog-classic-inner .pbmit-read-more-link a span:after, .pbmit-blog-style-2 .pbmit-read-more-link a span:after, .pbmit-blog-style-1 .pbmit-read-more-link a span:after {position: absolute;left: 0;top: 48%;content: '';width: 20px;height: 1px;background-color: #d02333;}.pbmit-blog-classic-inner .pbmit-read-more-link a:hover, .pbmit-blog-style-1 .pbmit-read-more-link a:hover, .pbmit-testimonial-style-2 blockquote, .pbmit-form-style-1 .input-button button:hover, .pbmit-testimonial-style-2 .pbminfotech-box-title, .pbmit-pricing-table-featured-col .pbmit-ptable-btn a:hover, .pbmit-blogbox-style-1 .pbmit-read-more-link a span:after {color: var(--pbmit-secondary-color);}.pbmit-blog-style-1 .pbmit-read-more-link a:hover span:after{background-color: var(--pbmit-secondary-color);}.pbmit-blog-style-2 .pbminfotech-box-content{padding: 20px 25px 10px;background: #fff;margin-left: 30px;top: -50px;position: relative;}.pbmit-blog-style-2 .pbmit-featured-container{position: relative;}.pbmit-blog-style-2 .pbminfotech-box-content .pbmit-post-title{font-size: 24px;line-height: 28px;margin-bottom: 15px;}.pbmit-blog-style-2 .pbminfotech-box-desc{margin-top: 10px;}.pbmit-blog-style-2 .pbmit-meta-date-wrapper{position: absolute;left: 20px;top: 20px;padding: 8px 15px;z-index: 9;background-color: #d02333;}.pbmit-blog-style-2 .pbmit-meta-date-wrapper span{display: block;color: #fff;text-transform: uppercase;font-family: Rajdhani,sans-serif;line-height: 36px;font-size: 18px;line-height: 18px;font-weight: 600 !important;letter-spacing: 1.5px;}.pbmit-blog-style-2 .pbmit-meta-date-wrapper span.pbmit-day{font-size: 34px;line-height: 34px;}.pbmit-blog-style-2 .pbmit-featured-wrapper {position: relative;display: block;overflow: hidden;}.pbmit-blog-style-2 .pbmit-featured-wrapper img {position: relative;width: 100%;-webkit-transition: all 0.5s ease 0s;-o-transition: all 0.5s ease 0s;transition: all 0.5s ease 0s;}.pbmit-blog-style-2:hover .pbmit-featured-wrapper img {-webkit-transform: scale(1.2);-ms-transform: scale(1.2);transform: scale(1.2);}.pbmit-testimonial-style-2 blockquote, .pbmit-testimonial-style-1 blockquote, .pbmit-testimonial-style-3 blockquote {font-family: Rajdhani,sans-serif;font-weight: normal;font-weight: 400;font-style: normal;}.pbmit-testimonial-style-1 .pbminfotech-box-content{position: relative;padding: 30px 30px 30px;border: 1px solid #3f4f5b;}.pbmit-testimonial-style-1 .pbminfotech-box-img{margin-top: 25px;align-items: center;margin-left: 50px;}.pbmit-testimonial-style-1 .pbminfotech-box-img img{border-radius: 50%;height: 70px;width: 70px !important;}.pbmit-testimonial-style-1 .pbminfotech-box-author {position: relative;text-align: left;display: inline-block;margin-left: 15px;}.pbmit-testimonial-style-1 .pbminfotech-box-title{font-size: 18px;line-height: 24px;margin-bottom: 0;margin-top: 0px;letter-spacing: 0;color: var(--pbmit-white-color);}.pbmit-testimonial-style-1 .pbminfotech-testimonial-detail{line-height: normal;font-size: 13px;font-weight: 500;letter-spacing: 1px;text-transform: uppercase;color: var(--pbmit-white-color);}.pbmit-testimonial-style-1 blockquote{font-size: 20px;line-height: 30px;padding: 0;margin: 0;border: 0;color: var(--pbmit-white-color);background: transparent;}.pbmit-testimonial-style-1 .pbminfotech-box-star-ratings{margin-bottom: 15px;font-size: 20px;}.pbmit-testimonial-style-1 .pbmit-base-icon-star.pbmit-active{color: #d02333;}.pbmit-testimonial-style-1 .pbmit-base-icon-star{color: var(--pbmit-white-color);}.pbmit-testimonial-style-1 .pbminfotech-box-content:before {content: "";width: 0px;height: 0px;position: absolute;border-left: 10px solid transparent;border-right: 10px solid #3f4f5b;border-top: 10px solid #3f4f5b;border-bottom: 10px solid transparent;left: 16px;bottom: -21px;}.pbmit-testimonial-style-1 .pbminfotech-box-content:after {content: "";width: 0px;height: 0px;position: absolute;border-left: 10px solid transparent;border-right: 10px solid #222d35;border-top: 10px solid #222d35;border-bottom: 10px solid transparent;left: 15px;bottom: -19px;}.pbmit-testimonial-style-2 .pbminfotech-box-content{position: relative;padding: 30px 40px 30px;box-shadow: 0px 7px 13px 0px rgba(0, 0, 0, 0.05);background-color: #fff;}.pbmit-testimonial-style-2 .pbminfotech-box-desc{position: relative;padding-top: 35px;}.pbmit-testimonial-style-2 .pbminfotech-box-desc:after {position: absolute;content: '"';font-size: 70px;line-height: 70px;top: -40px;left: 0;color: #d8d8d8;font-weight: 700 !important;transform: rotate(180deg);}.pbmit-testimonial-style-2 .pbminfotech-box-img{margin-top: 30px;align-items: center;margin-left: 50px;}.pbmit-testimonial-style-2 .pbminfotech-box-img img{border-radius: 50%;height: 50px;width: 50px !important;}.pbmit-testimonial-style-2 .pbminfotech-box-author {position: relative;text-align: left;display: inline-block;margin-left: 15px;}.pbmit-testimonial-style-2 .pbminfotech-box-title{font-size: 18px;line-height: 24px;margin-bottom: 0;margin-top: 0px;letter-spacing: 0;}.pbmit-testimonial-style-2 .pbminfotech-testimonial-detail{line-height: normal;font-size: 13px;font-weight: 500;letter-spacing: 1px;text-transform: uppercase;color: #606060;}.pbmit-testimonial-style-2 blockquote{font-size: 18px;line-height: 22px;padding: 0;margin: 0;border: 0;background: transparent;font-style: italic !important;}.pbmit-testimonial-style-2 .pbminfotech-box-star-ratings{margin-top: 15px;}.pbmit-testimonial-style-2 .pbmit-base-icon-star.pbmit-active{color: #c3002f;}.pbmit-testimonial-style-2 .pbminfotech-box-content:before {position: absolute;top: 100%;left: 30px;margin-left: -8px;content: "";width: 0;height: 0;border: 12px solid;border-color: #fff #fff transparent transparent;-webkit-transform-origin: 0 0;-ms-transform-origin: 0 0;transform-origin: 0 0;box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.05);}.pbmit-testimonial-style-3 .pbminfotech-box-content{position: relative;padding: 30px 30px 30px;box-shadow: 0 10px 30px rgba(0,0,0,.01);background-color: #fff;}.pbmit-testimonial-style-3 .pbminfotech-box-desc{position: relative;}.pbmit-testimonial-style-3 .pbminfotech-box-desc:after {position: absolute;content: '"';font-size: 200px;line-height: 200px;bottom: 55px;right: -10px;opacity: 0.1;font-weight: 700;font-family: Rajdhani,sans-serif;transform: rotate(180deg);color: #d02333;}.pbmit-testimonial-style-3 .pbminfotech-box-img{margin-top: 30px;align-items: center;margin-left: 30px;}.pbmit-testimonial-style-3 .pbminfotech-box-img img{border-radius: 50%;height: 70px;width: 70px !important;}.pbmit-testimonial-style-3 .pbminfotech-box-author {position: relative;text-align: left;display: inline-block;margin-left: 15px;}.pbmit-testimonial-style-3 .pbminfotech-box-title{font-size: 18px;line-height: 22px;margin-bottom: 0;margin-top: 0px;letter-spacing: 0;color: #d02333;}.pbmit-testimonial-style-3 .pbminfotech-testimonial-detail{line-height: normal;font-size: 13px;font-weight: 500;letter-spacing: 1px;text-transform: uppercase;}.pbmit-testimonial-style-3 blockquote{font-size: 18px;line-height: 24px;padding: 0;margin: 0;border: 0;background: transparent;color: #222d35;}.pbmit-testimonial-style-3 .pbminfotech-box-content:before {position: absolute;top: 100%;left: 60px;margin-left: -8px;content: "";width: 0;height: 0;border-left: 10px solid transparent;border-right: 10px solid transparent;border-top: 18px solid #fff;-webkit-transform-origin: 0 0;-ms-transform-origin: 0 0;transform-origin: 0 0;}.pbmit-client-wrapper {position: relative;text-align: center;z-index: 1;height: -webkit-max-content;display: inline-block;-webkit-transition: -webkit-transform .4s ease;transition: -webkit-transform .4s ease;-o-transition: transform .4s ease;transition: transform .4s ease;transition: transform .4s ease,-webkit-transform .4s ease;}.pbmit-client-style-1{text-align: center;margin-bottom: 15px;}.pbmit-client-style-1 .pbmit-client-wrapper {overflow: hidden;}.pbmit-client-style-1 .pbmit-client-with-hover-img .pbmit-featured-wrapper, .pbmit-client-style-1 .pbmit-client-hover-img{-webkit-transition: -webkit-transform .4s ease;transition: -webkit-transform .4s ease;-o-transition: transform .4s ease;transition: transform .4s ease;transition: transform .4s ease, -webkit-transform .4s ease;}.pbmit-client-style-1 .pbmit-client-hover-img {position: absolute;z-index: 1;top: 0;left: 0;transform: translateY(-100%);}.pbmit-client-style-1 .pbmit-client-with-hover-img .pbmit-client-hover-img {transform: translateY(-100%);}.pbmit-client-style-1 .pbmit-client-with-hover-img:hover .pbmit-client-hover-img {visibility: visible;transform: translateY(0%);}.pbmit-client-style-1 .pbmit-client-with-hover-img:hover .pbmit-featured-wrapper{transform: translateY(100%);}.pbmit-client-style-2{text-align: center;}.pbmit-client-style-2 .pbmit-client-wrapper {overflow: hidden;border: 1px solid rgb(0 0 0 / 10%);padding: 15px 20px;}.pbmit-client-style-2 .pbmit-client-with-hover-img .pbmit-featured-wrapper, .pbmit-client-style-2 .pbmit-client-hover-img{-webkit-transition: -webkit-transform .4s ease;transition: -webkit-transform .4s ease;-o-transition: transform .4s ease;transition: transform .4s ease;transition: transform .4s ease, -webkit-transform .4s ease;}.pbmit-client-style-2 .pbmit-client-hover-img {position: absolute;z-index: 1;top: 0;left: 0;transform: translateY(-100%);}.pbmit-client-style-2 .pbmit-client-with-hover-img .pbmit-client-hover-img {transform: translateY(-100%);padding: 15px 20px;}.pbmit-client-style-2 .pbmit-client-with-hover-img:hover .pbmit-client-hover-img {visibility: visible;transform: translateY(0%);}.pbmit-client-style-2 .pbmit-client-with-hover-img:hover .pbmit-featured-wrapper{transform: translateY(130%);}.site-title {margin: 0;padding: 0;vertical-align: middle;text-align: center;width: 100%;}.site-title {height: 120px;line-height: 120px;}.pbmit-slider-area {background-color: #e7e7e7;position: relative;z-index: 1;}.pbmit-header-search-btn a {font-size: 22px;padding: 0 20px;}.site-header .pbmit-responsive-icons .pbmit-header-search-btn a {display: none;}.home .header-style-1{padding-bottom: 30px;}.home .header-style-1:before {content: '';width: 200px;height: 200px;background: url(../images/download.png) repeat 0 0;position: absolute;bottom: 0;left: 0;}.header-style-1:after {content: '';width: 40%;height: 100%;position: absolute;right: 0;top: 0;z-index: -1;background-color: #d02333;}.header-style-1 .pbmit-logo-menuarea {display: -ms-flexbox!important;display: flex!important;}.header-style-1 .site-branding {margin-right: 80px;}.header-style-1 .site-navigations ul.navigation > li > a{margin: 0 15px;}.header-style-1 .pbmit-header-search-btn a i{color: var(--pbmit-white-color);}.header-style-1 .pbmit-header-button {line-height: normal;font-weight: 400;font-style: normal;font-family: Rajdhani,sans-serif;}.header-style-1 .pbmit-header-button a {color: #fff;height: 100%;display: inline-block;padding: 0 60px;vertical-align: middle;padding-right: 8px;font-weight: normal;font-size: 16px;position: relative;border-radius: 6px;letter-spacing: 1px;-webkit-transition: none;transition: none;}.header-style-1 .pbmit-header-button a:before {content: "\e848";font-family: 'pbminfotech-base-icons';font-size: 45px;line-height: 45px;top: 3px;position: absolute;left: 0;color: #fff;font-weight: normal;}.header-style-1 .pbmit-header-button-text-1 {font-weight: 700;margin-bottom: 5px;}.header-style-1 .pbmit-header-button-text-2{font-weight: 600;}.header-style-1 .pbmit-header-button a span {display: block;-webkit-transition: all .25s ease-in-out;transition: all .25s ease-in-out;}.pbmit-right-side{display: flex;align-items: center;}.header-style-1 .pbmit-social-links {position: absolute;list-style: none;top: 40%;padding: 0;left: 40px;display: block;}.header-style-1 .pbmit-social-links li {margin: 20px 0;}.header-style-1 .pbmit-slider-area {margin-left: 100px;margin-right: 100px;}.header-style-1 .sticky-header .site-title {height: 90px;line-height: 90px;}.header-style-1 .sticky-header .pbmit-header-button a {color: #222d35;}.header-style-1 .sticky-header .pbmit-header-button a:before{color: #222d35;}.header-style-1 .sticky-header .pbmit-header-search-btn a i {color: #09162a;}.header-style-2 .pbmit-header-info-inner .pbmit-header-box {display: inline-block;vertical-align: middle;margin-right: 70px;position: relative;padding: 10px 0 10px 60px;}.pbmit-header-box span {display: block;}.header-style-2 .pbmit-header-info-inner .pbmit-header-box-icon i {position: absolute;top: 18px;left: 0;font-size: 35px;height: 40px;width: 40px;line-height: 35px;text-align: center;background-color: transparent;border-radius: 50%;color: #d02333;}.pbmit-header-box-title {font-family: Rajdhani,sans-serif;font-weight: 500;font-size: 15px;line-height: 27px;letter-spacing: 0;color: #0c121d;text-transform: none;font-weight: 500;font-style: normal;}.pbmit-header-box-content {font-family: Rajdhani,sans-serif;font-weight: 600;font-size: 15px;line-height: 25px;letter-spacing: 1px;color: #000;text-transform: none;font-weight: 600;font-style: normal;}.header-style-2 .pbmit-header-info-inner .pbmit-header-box:first-child:before{display: none;}.header-style-2 .pbmit-header-info-inner .pbmit-header-box:before {content: '';width: 1px;height: 100%;position: absolute;background-color: #eaeaea;left: -40px;top: 0;}.header-style-2 .pbmit-header-info-inner .pbmit-header-box:last-child {margin-right: 0;}.header-style-2 .site-header-menu .site-branding{display: none;}.header-style-2 .sticky-header{display: none;}.header-style-2 .pbmit-btn{line-height: 70px;height: 70px;padding: 0 30px;font-size: 13px;}.header-style-2 .pbmit-btn span::before{margin-top: 34px;}.header-style-2 .pbmit-btn:hover{background-color: var(--pbmit-secondary-color);}.header-style-2 .site-navigations{flex-grow: 1;display: flex;position: relative;padding-left: 15px;margin-left: 15px;background-color: var(--pbmit-light-color);}.header-style-2 .site-navigations ul.navigation > li > a{height: 70px;line-height: 70px;}.header-style-2 .pbmit-header-search-btn a {display: inline-block;font-size: 16px;height: 70px;line-height: 70px;padding: 0 20px;background-color: #d02333;color: var(--pbmit-white-color);}.header-style-2 .site-navigations:after {content: '';width: 5000px;height: 70px;position: absolute;right: -5000px;top: 0;background-color: var(--pbmit-light-color);}.header-style-3 .site-header-menu {position: absolute;width: 100%;z-index: 2;padding: 0 40px;border-bottom: 1px solid rgba(255,255,255,.23);}.header-style-3 .site-navigations ul.navigation > li > a{color: var(--pbmit-white-color);font-size: 15px;}.header-style-3 .site-branding .sticky-logo {display: none;}.header-style-3.site-header .sticky-header .site-branding img{max-height: 45px;}.header-style-3 .sticky-header .site-branding .sticky-logo {display: block;}.header-style-3 .sticky-header .site-branding .logo-img{display: none;}.header-style-3 .pbmit-logo-menuarea{display: flex;align-items: center;-webkit-box-pack: justify;-ms-flex-pack: justify;justify-content: space-between;}.header-style-3 .site-branding{margin-right: 30px;}.header-style-3 .pbmit-right-side {line-height: 120px;height: 120px;}.header-style-3 .pbmit-header-button {padding-left: 30px;border-left: 1px solid rgba(255,255,255,.23);}.header-style-3 .pbmit-social-links {list-style: none;margin: 0;margin-right: 60px;padding-left: 0;}.header-style-3 .pbmit-social-links li {display: inline-block;margin: 0 15px;}.header-style-3 .pbmit-social-links li a {color: #fff;}.header-style-3 .pbmit-header-search-btn a{color: var(--pbmit-white-color);}.header-style-3 .pbmit-header-button a {color: #fff;height: 100%;display: inline-block;padding: 0 70px;padding-right: 8px;vertical-align: middle;font-weight: normal;font-size: 16px;position: relative;border-radius: 6px;letter-spacing: 1px;line-height: normal;-webkit-transition: all .25s ease-in-out;transition: all .25s ease-in-out;}.header-style-3 .pbmit-header-button a:after {content: "\E847";font-family: 'pbminfotech-base-icons';font-size: 60px;line-height: 60px;top: -2px;position: absolute;left: 0;color: #c3002f;font-weight: normal;}.header-style-3 .pbmit-header-button a span {display: block;}.header-style-3 .pbmit-header-button .pbmit-header-button-text-1 {font-size: 20px;font-weight: 700;margin-bottom: 5px;font-family: Rajdhani,sans-serif;}.header-style-3 .pbmit-header-button .pbmit-header-button-text-2 {font-size: 20px;font-family: Rajdhani,sans-serif;}.header-style-3 .sticky-header .site-title, .header-style-3 .sticky-header .pbmit-right-side {line-height: 90px;height: 90px;}.header-style-3 .sticky-header .site-navigations ul.navigation > li > a, .header-style-3 .sticky-header .pbmit-header-button a, .header-style-3 .sticky-header .pbmit-header-search-btn a, .header-style-3 .sticky-header .pbmit-social-links li a{color: #09162a;}.header-style-3 .sticky-header .site-navigations ul li.active > a {color: #d02333;}.header-style-3 .sticky-header .pbmit-header-button {border-left: 1px solid rgba(0,0,0,.13);}.site-footer {font-size: 15px;}.footer.site-footer {color: rgba(255,255,255,.9);background-color: var(--pbmit-blackish-color);background-image: url(../images/footer-patten-bg.png);background-repeat: no-repeat;background-position: center top;background-size: contain;background-attachment: scroll;}.footer-wrap.pbmit-footer-big-area {padding-top: 80px;padding-bottom: 80px;border-bottom: 1px solid rgb(255 255 255 / 5%);}.pbmit-footer-contact-info-inner i {display: inline-block;width: 85px;height: 85px;line-height: 85px;text-align: center;margin-right: 25px;font-size: 30px;color: #fff;font-weight: 400;background-color: #d02333;}.pbmit-footer-big-area .pbmit-footer-contact-info-wrap {font-size: 22px;line-height: 32px;letter-spacing: -.5px;font-weight: 500;color: #d02333;font-family: Rajdhani,sans-serif;}.pbmit-footer-contact-info-inner .pbmit-label {display: block;font-size: 16px;font-weight: 600;line-height: 24px;color: #fff;}.pbmit-footer-widget-col-1, .pbmit-footer-widget-col-3{-ms-flex: 0 0 25%;flex: 0 0 25%;max-width: 25%;position: relative;}.pbmit-footer-widget-col-2{-ms-flex: 0 0 50%;flex: 0 0 50%;max-width: 50%;}.site-footer .widget {padding: 70px 0 60px;}.site-footer .widget-title {font-family: Rajdhani,sans-serif;font-weight: 600;font-size: 24px;line-height: 26px;letter-spacing: -.5px;text-transform: none;font-weight: 600;font-style: normal;margin-bottom: 40px;color: var(--pbmit-white-color);}.widget ul a {display: inline-block;}.widget ul>li {padding: 0 0 15px 0;}.site-footer .textwidget ul li a{color: #b8c3cb;}.site-footer .textwidget ul li a:hover{color: var(--pbmit-white-color);}.widget.pbmit-footer-widget-col-1 {padding-left: 15px;padding-right: 45px;}.site-footer .widget.pbmit-footer-widget-col-2 {padding-right: 45px;padding-left: 45px;position: relative;}.footer-newsletter-links input{padding: 0 15px;}.footer-newsletter-links .form-control{height: 60px;}.footer-newsletter-links button {background-color: #c3002f;color: #fff;border: none;padding: 21px 40px;padding-left: 65px;position: relative;font-size: 14px;line-height: 18px;letter-spacing: 1px;text-transform: uppercase;font-weight: 600;border-radius: 0;font-family: Rajdhani,sans-serif;}.footer-newsletter-links button:before, .pbmit-newsletter button::before{content: ' ';width: 15px;height: 1px;background-color: var(--pbmit-white-color);margin-right: 10px;position: absolute;left: 40px;top: 50%;}.pbmit-footer-widget-col-3 {padding: 0 15px 0 45px;}.pbmit-timelist-list {padding: 0;margin: 0;}ul.pbmit-timelist-list li:first-child {padding-top: 0;}ul.pbmit-timelist-list li {list-style: none;padding: 8px 0;}.pbmit-timelist-list .pbmit-timelist-li-value {float: right;color: #b8c3cb;}.site-footer .pbmit-footer-widget-col-2:after {content: "";position: absolute;width: 1px;height: 100%;right: 0;top: 0;background-color: rgb(255 255 255 / 5%);}.site-footer .pbmit-footer-widget-col-1:after {content: "";position: absolute;width: 1px;height: 100%;right: 0;top: 0;background-color: rgb(255 255 255 / 5%);}.site-footer .pbmit-footer-widget-col-3 .widget{padding-left: 35px;}.footer-social-links {border-top: 1px solid rgb(255 255 255 / 5%);margin-top: 50px;margin-left: -45px;margin-right: -45px;padding-top: 60px;padding-bottom: 0;}.footer-social-links .pbmit-social-links {margin-top: -15px;}.footer-social-links .pbmit-social-links li {display: inline-block;}.footer-social-links .pbmit-social-links li a {display: inline-block;background-color: #fff;width: 40px;height: 40px;line-height: 40px;margin: 0 2px;text-align: center;color: #333;border-radius: 50%;}.footer-social-links .pbmit-social-links li a:hover {color: #fff;background-color: #d02333;}.pbmit-footer-bottom {padding: 15px 0 30px;border-top: 1px solid rgb(255 255 255 / 5%);}.pbmit-footer-copyright-text-area {padding-top: 20px;}.pbmit-footer-copyright-text-area a {color: #b8c3cb;}.pbmit-footer-copyright-text-area a:hover{color: var(--pbmit-white-color);}.pbmit-footer-menu-area {text-align: right;margin-top: 20px;}.pbmit-footer-menu {list-style: none;margin: 0;padding: 0;}.pbmit-footer-menu li {display: inline-block;margin: 0 10px;}.pbmit-footer-menu-area .menu-item a {color: #b8c3cb;}.pbmit-footer-menu-area .menu-item a:hover{color: var(--pbmit-white-color);}.pbmit-title-bar-wrapper:before {position: absolute;content: "";display: block;position: absolute;top: 0;left: 0;width: 100%;height: 100%;background-color: transparent;background-image: none!important;background-repeat: no-repeat;background-position: center center;background-size: cover;background-attachment: scroll;}.pbmit-title-bar-wrapper {background-color: transparent;background-image: url(../images/title-01.jpg);background-repeat: no-repeat;background-position: top center;background-size: cover;background-attachment: scroll;position: relative;}.pbmit-title-bar-content {position: relative;z-index: 1;display: flex;align-items: center;padding: 64px 0;min-height:300px;}.pbmit-title-bar-content .pbmit-tbar-inner {max-width: none;padding: 0;}.pbmit-title-bar-content-inner {width: 100%;text-align: center;}.pbmit-title-bar-content .pbmit-tbar-title {font-weight: 600;font-size: 46px;letter-spacing: -.5px;color: var(--pbmit-white-color);text-transform: none;font-style: normal;margin-bottom: 5px;}.pbmit-breadcrumb, .pbmit-breadcrumb a {font-family: var(--pbmit-heading-typography-font-family);font-weight: 600;font-size: 16px;line-height: 1.5;text-transform: uppercase;color: var(--pbmit-white-color);text-transform: none;font-style: normal;opacity: .9;}.pbmit-breadcrumb-inner .sep {margin: 0 2px;}.pbmit-breadcrumb-inner i {font-size: 13px;margin: 0 10px;display: inline-block;}.overlap-colomn {position: relative;}.overlap-wrapper {position: absolute;height: 100%;width: 100%;top: 0;left: 0;z-index: 9;}.overlap-img, .overlap-bg {position: absolute;width: 100%;height: 100%;}.overlap-left {margin-left: -500px;}.overlap-right {margin-right: -500px;width: auto;left: 0;right: 0;}.overflow-hidden {overflow: hidden;}.content-element-text {position: relative;z-index: 99;padding-top: 60px;padding-bottom: 40px;padding-right: 30px;}.progressbar{padding-bottom: 25px;overflow: hidden;}.progressbar .progress-label{display: inline-block;font-size: 18px;line-height: 28px;font-weight: 600;font-family: Rajdhani,sans-serif;}.progress .progress-bar {position: relative;overflow: visible;border-radius: 0px;background-color: #d02333;}.progress.progress-percent-bg .progress-percent {position: absolute;right: 0;top: -30px;font-size: 15px;color: #222d35;font-weight: 600;}.progressbarone .progress-bar{background-color: var(--pbmit-blackish-color);}.progressbar:last-child{padding-bottom: 0;}.accordion{background: none;border-radius: 0;padding-top: 0px;}.accordion-item{border: none;color: var(--pbmit-blackish-color);}.accordion .accordion-item h2 {font-size: 16px;font-weight: 600;line-height: 1;background-color: transparent;border-bottom: 1px solid #222d35;}.accordion .accordion-item h2:hover .accordion-button {color: #d02333;}.accordion-button {font-size: 16px;font-weight: 600;border: none;border-radius: 0!important;box-shadow: none;padding: 25px 0 15px;background: none;outline: none;color: var(--pbmit-secondary-color);transition: color .2s ease-in-out;}.accordion-button::after{background-image: none;transition: none;content: '\e818';font-family: "pbminfotech-base-icons";}.accordion-button:not(.collapsed)::after {background-image: none;font-size: 14px;content: "\f068";font-family: 'FontAwesome';transform:none;}.accordion-item.active .accordion-button{border-bottom: 2px solid #222d35;font-weight: 700;}.accordion-item.active .accordion-button::after{font-size: 14px;content: "\f068";font-family: 'FontAwesome';}.accordion-button:focus{box-shadow:none;border-color: #d4d4d4;}.accordion-button:not(.collapsed) {color: var(--pbmit-blackish-color);background-color: transparent;box-shadow: none;transition: none;}.accordion-body{color: var(--pbmit-body-typography-color);font-weight: 400;padding: 10px 0 0 ;border: solid transparent;-webkit-transform: translate3d(0,0,0);transform: translate3d(0,0,0);transition: padding .2s ease-in-out;}.accordion-style-1{padding-bottom: 30px;}.accordion-style-1 .accordion-item.active .accordion-button{color: var(--pbmit-white-color);background-color: #d02333;}.accordion-style-1 .accordion-button{padding: 17px 20px 17px 35px;color: #222;background-color: #f1f1f1;z-index: 0;}.accordion-style-1 .accordion-item{margin-bottom: 30px;}.accordion-style-1 .accordion-item h2{border-bottom: 0;transition: color .2s ease-in-out;}.accordion-style-1 .accordion-body{padding: 30px 55px 35px 40px;background-color: #fbfbfb;color: #222;}.accordion-style-1 .accordion-item.active i{color: var(--pbmit-white-color);}.accordion-style-1 .accordion-item i{color: #666;}.accordion-style-1 .accordion-item.active .accordion-button::after{color: var(--pbmit-white-color);}.pbmit-tab-style-1{margin-bottom: 21.73913043px;}.pbmit-tab-style-1 .nav-tabs{margin-bottom: 30px;border-bottom-color: #e5e8ef;}.pbmit-tab-style-1 .nav-item{-webkit-box-flex: 1;-ms-flex: 1 1 auto;flex: 1 1 auto;text-align: center;}.pbmit-tab-style-1 .nav-item a.active{border-bottom: 2px solid #d02333;}.pbmit-tab-style-1 .nav-item a.active i{color: #d02333;}.pbmit-tab-style-1 .nav-item a.active span{color: var(--pbmit-blackish-color);}.pbmit-tab-style-1 .nav-item a{color: var(--pbmit-body-typography-color);border: none;font-weight: 700;padding: 14px 20px;}.pbmit-tab-style-1 .nav-item .nav-link i{font-size: 24px;vertical-align: middle;}.pbmit-tab-style-1 .nav-item .nav-link span{margin-left: 14px;}.pbmit-tab-style-1 .pbmit-column-inner{padding: 14px 20px;}.pbmit-tab-style-1 .single-image-wrapper img{box-shadow: 0 0 20px rgb(79 110 173 / 10%);border-radius: 8px;}.pbmit-tab-style-1 .content-wrapper h3{font-size: 24px;line-height: 28px;margin-bottom: 30px;}.pbmit-tab-style-1 .content-wrapper p{margin-bottom: 35px;}.pbmit-tab-style-1 .list-group{margin-bottom: 0;}.db-circle-overlay {position: absolute;top: 50%;-khtml-transform: translateX(0%) translateY(-50%);-moz-transform: translateX(0%) translateY(-50%);-ms-transform: translateX(0%) translateY(-50%);-o-transform: translateX(0%) translateY(-50%);transform: translateX(0%) translateY(-50%);left: 0;width: 100%;text-align: center }.db-fidbox-style-2 .db-circle-w {position: relative;text-align: center }.db-fidbox-style-2 .db-fid-title {text-align: center;color: var(--pbmit-white-color);font-size: 14px;font-weight: 600;margin-top: 0 }.db-fidbox-style-2 .db-circle-number sub, .db-fidbox-style-2 .db-circle-number {font-size: 20px;color: var(--pbmit-white-color);font-weight: bold }.db-fidbox-style-2 .db-circle-number sub {bottom: 0 }.db-fidbox-style-1 .db-fid-title {font-size: 18px;line-height: 20px;margin-top: 0 }.db-fidbox-style-1 .db-fid-title-w, .db-fidbox-style-1 .db-circle-w {display: inline-block }.db-fidbox-style-1 .db-fid-title-w {width: 43%;margin-left: 12px }.db-fidbox-style-1 .db-circle-w {width: 122px;position: relative;vertical-align: top }.db-fidbox-style-1 .db-circle-number sub, .db-fidbox-style-1 .db-circle-number {font-size: 20px;font-weight: bold }.db-fidbox-style-1 .db-circle-number sub {bottom: 0 }.db-fidbox-style-1 .db-fid-title-w {position: absolute;top: 50%;-khtml-transform: translateX(0%) translateY(-50%);-moz-transform: translateX(0%) translateY(-50%);-ms-transform: translateX(0%) translateY(-50%);-o-transform: translateX(0%) translateY(-50%);transform: translateX(0%) translateY(-50%) }.db-fidbox-style-1 .db-fid-title-w h3 {margin-bottom: 0 }.db-circle canvas {image-rendering: optimizeSpeed;image-rendering: -moz-crisp-edges;image-rendering: -webkit-optimize-contrast;image-rendering: -o-crisp-edges;image-rendering: pixelated;-ms-interpolation-mode: nearest-neighbor;}.db-circle canvas {max-width: 100%;height: auto !important }.db-overlap-row {position: relative;z-index: 1 }.db-overlap-row-section {position: relative;z-index: 2 }.list-group-borderless .list-group-item{border: none;color: var(--pbmit-body-typography-color);padding: 0;padding-bottom: calc(12px/2);background-color: transparent!important;border: none;font-family: "Roboto", Sans-serif;font-size: 16px;font-weight: 400;display: flex;align-items: center;}.list-group-borderless .list-group-item:not(:first-child){margin-top: calc(15px/2);}ul.list-group {margin-bottom: 1.75em;}li.list-group-item {background-color: transparent;border: none;font-weight: 500;padding: 0;padding-bottom: 10px;}ul.list-group .list-group-item i{font-size: 10px;padding-right: 20px;color: #d02333;}.pbmit-pricing-table-box {text-align: center;background-color: transparent;border: 1px solid #eee;position: relative;}.pbmit-pricing-table-box .pbmit-ptable-icon-wrapper {line-height: 80px;font-size: 80px;color: #d02333;}.pbmit-pricing-table-box .pbmit-ptable-icon{font-size: 26px;line-height: 32px;margin-top: 10px;margin-bottom: 0;padding: 40px 0px 0px 0px;}.pbmit-pricing-table-box .pbminfotech-ptable-heading{font-size: 26px;line-height: 32px;font-weight: 400;text-transform: uppercase;margin-bottom: 0;display: inline-block;padding: 2px 10px;background: #ffff;}.pbmit-pricing-table-box .pbmit-head-wrap{margin: 20px 30px 30px;padding: 10px 0px 20px 00px;text-align: center;position: relative;z-index: 2;}.pbmit-pricing-table-box .pbmit-head-wrap:after{content: '';width: 100%;height: 1px;background: #eee;position: absolute;top: 27px;left: 0;z-index: -1;}.pbmit-pricing-table-box .pbminfotech-ptable-price-w{position: relative;margin: 0 auto 30px;display: inline-block;font-family: Rajdhani,sans-serif;}.pbmit-pricing-table-box .pbminfotech-ptable-price {font-size: 60px;line-height: 60px;font-weight: 700;}.pbmit-pricing-table-box .pbmit-ptable-lines-w {padding: 15px 50px;margin-top: 0;text-align: left;}.pbmit-pricing-table-box .pbmit-ptable-btn {margin: 20px 0 40px;padding: 0 50px;}.pbmit-ptable-btn a{padding: 20px 30px;display: block;color: #fff;}.pbmit-ptable-btn a span{position: relative;display: inline-block;padding-left: 30px;}.pbmit-ptable-btn a span:after {position: absolute;left: 5px;top: 48%;content: '';width: 15px;height: 1px;background: #fff;transition: all .3s ease-in-out;}.pbmit-pricing-table-box .pbminfotech-sep {height: 1px;background: rgba(0, 0, 0, 0.1);display: none;}.pbmit-pricing-table-box .pbminfotech-ptable-frequency {position: absolute;bottom: 4px;display: inline-block;font-size: 16px;font-weight: 500;margin-left: 6px;color: #606060;}.pbmit-pricing-table-box .pbminfotech-ptable-frequency:before {content: '/';margin-right:4px;}.pbmit-pricing-table-box .pbminfotech-ptable-symbol, .pbmit-pricing-table-box .pbminfotech-ptable-price {display: inline-block;}.pbmit-pricing-table-box .pbminfotech-ptable-symbol {position: absolute;top: 2px;left: -18px;font-size: 26px;line-height: 26px;font-weight: 600;}.pbmit-pricing-table-box .pbmit-ptable-line i{margin-left: 10px;float: right;color: #d02333;}.pbmit-pricing-table-box .pbmit-ptable-line {position: relative;margin-bottom: 10px;}.pbmit-pricing-table-featured-col .pbmit-pricing-table-box .pbmit-ptable-icon-wrapper{color: var(--pbmit-white-color);}.pbmit-pricing-table-featured-col .pbmit-ptable-btn a span::before , .pbmit-pricing-table-featured-col .pbmit-ptable-btn .pbmit-btn span::before{display: none;}.pbmit-pricing-table-featured-col .pbmit-ptable-btn a:hover span:after{background-color: var(--pbmit-secondary-color);}.pbmit-pricing-table-featured-col .pbmit-pricing-table-box .pbmit-ptable-lines-w, .pbmit-pricing-table-featured-col .pbmit-pricing-table-box .pbminfotech-ptable-symbol, .pbmit-pricing-table-featured-col .pbmit-pricing-table-box .pbminfotech-ptable-frequency, .pbmit-pricing-table-featured-col .pbmit-pricing-table-box .pbminfotech-ptable-price, .pbmit-pricing-table-featured-col .pbmit-pricing-table-box .pbminfotech-ptable-heading{color: #fff;}.pbmit-pricing-table-featured-col .pbmit-ptable-btn a{color: #fff;}.pbmit-pricing-table-featured-col .pbmit-ptable-btn a:hover{background-color: #fff;}.pbmit-pricing-table-featured-col .pbmit-pricing-table-box:before {content: "";position: absolute;right: 0;top: 0;color: #fcdc31;border-top: 48px solid #000;border-right: 50px solid #000;border-left: 48px solid transparent;border-bottom: 51px solid transparent;}.pbmit-pricing-table-featured-col .pbmit-pricing-table-box .pbmit-ptablebox-featured-w {background-color: transparent;vertical-align: middle;z-index: 20;position: absolute;transform: rotate(45deg);transform-origin: 0 top;display: block;top: -19px;right: -50px;width: 100px;margin: 0;padding-top: 10px;text-align: center;color: #fff;text-transform: uppercase;font-weight: 600;font-size: 14px;}.pbmit-pricing-table-featured-col .pbmit-pricing-table-box .pbmit-head-wrap:after{background: #374149;}.pbmit-pricing-table-featured-col .pbmit-pricing-table-box .pbmit-ptable-icon{color: #fff;}.pbmit-pricing-table-featured-col .pbmit-pricing-table-box{background-color: var(--pbmit-secondary-color);}.pbmit-pricing-table-featured-col .pbmit-pricing-table-box .pbmit-ptable-icon {color: #fff;}.pbmit-pricing-table-featured-col .pbmit-pricing-table-box:before {border-top: 48px solid #c3002f;border-right: 50px solid #c3002f;}.pbmit-pricing-table-featured-col .pbmit-pricing-table-box .pbminfotech-ptable-heading {color: #fff;background-color: var(--pbmit-secondary-color);}.pbmit-sortable-list a.pbmit-selected{color: #d02333;}.pbmit-sortable-list-ul{list-style: none;text-align: center;margin: 0;padding: 0;margin-bottom: 42px;display: block;}.pbmit-sortable-list-ul li{display: inline-block;}.pbmit-sortable-list-ul li:before {content: '/';margin: 0 15px;}.pbmit-sortable-list-ul li:first-child:before{display: none;}.pbmit-sortable-list a{font-size: 16px;font-family: Rajdhani,sans-serif;font-weight: 600;}.pbmit-category-style1 .col-md-20percent {-webkit-box-flex: 0;-ms-flex: 0 0 20%;flex: 0 0 20%;max-width: 20%;}.pbmit-category-style1 .pbmit-portfolio-style-2 {padding-left: 0;}#rev_slider_1_1_wrapper .rev-button{position:relative;display:inline-block;padding-left:30px }#rev_slider_1_1_wrapper .rev-button:before{position:absolute;left:5px;top:48%;content:'';width:15px;height:1px;background:#fff;z-index:9 }@media (max-width:767px){.rev-button:before{content:none;padding-left:1px }.rev-button{padding-left:0px !important;font-size:12px !important }}#rev_slider_1_1_wrapper .custom.tparrows{cursor:pointer;background:#000;background:rgba(0,0,0,0.5);width:40px;height:40px;position:absolute;display:block;z-index:1000 }#rev_slider_1_1_wrapper .custom.tparrows.rs-touchhover{background:#000 }#rev_slider_1_1_wrapper .custom.tparrows:before{font-family:'revicons';font-size:15px;color:#fff;display:block;line-height:40px;text-align:center }#rev_slider_1_1_wrapper .custom.tparrows.tp-leftarrow:before{content:'\e824' }#rev_slider_1_1_wrapper .custom.tparrows.tp-rightarrow:before{content:'\e825' }.custom.tp-bullets:before{content:' ';position:absolute;width:100%;height:100%;background:transparent;padding:10px;margin-left:-10px;margin-top:-10px;box-sizing:content-box }.custom .tp-bullet{width:12px;height:12px;position:absolute;background:#aaa;background:rgba(125,125,125,0.5);cursor:pointer;box-sizing:content-box }.custom .tp-bullet.rs-touchhover, .custom .tp-bullet.selected{background:rgb(125,125,125) }.header-style-1 .tparrows {bottom: -120px!important;top: auto!important;width: 60px!important;background-color: transparent!important;}.header-style-1 .custom.tparrows.tp-rightarrow {text-align: right;padding-right: 10px;}.header-style-1 .custom.tparrows.tp-leftarrow:after {content: "PREV"!important;margin-left: 10px;}.header-style-1 .custom.tparrows.tp-leftarrow:before {content: "\E845"!important;font-family: "pbminfotech-base-icons"!important;left: 0;}.header-style-1 .custom.tparrows.tp-rightarrow:after {content: "NEXT"!important;margin-right: 10px;}.header-style-1 .custom.tparrows.tp-rightarrow:after {color: #fff!important;}.header-style-1 .custom.tparrows.tp-leftarrow {text-align: left;padding-left: 10px;}.header-style-1 .custom.tparrows.tp-leftarrow:after, .header-style-1 .custom.tparrows.tp-rightarrow:after {display: inline-block;color: #fff!important;font-size: 13px!important;font-weight: 700!important;}.header-style-1 .custom.tparrows.tp-leftarrow:before, .header-style-1 .custom.tparrows.tp-rightarrow:before {display: inline-block!important;font-size: 13px!important;font-weight: 700!important;position: absolute;line-height: normal!important;top: 8px;}.header-style-1 .custom.tparrows.tp-rightarrow:before {content: "\E846"!important;font-family: "pbminfotech-base-icons"!important;right: 0;}.dsvy-skincolor{color: #d02333;}.rev-button{position:relative;display:inline-block;padding-left:30px }#rev_slider_2_1_wrapper .rev-button:before{position:absolute;left:5px;top:48%;content:'';width:15px;height:1px;background:#fff;z-index:9 }@media (max-width:767px){.rev-button:before{content:none;padding-left:1px }.rev-button{padding-left:0px !important;font-size:12px !important }}#rev_slider_2_1_wrapper .uranus .tp-bullet{border-radius:50%;box-shadow:0 0 0 2px rgba(255,255,255,0);-webkit-transition:box-shadow 0.3s ease;transition:box-shadow 0.3s ease;background:transparent;width:15px;height:15px }#rev_slider_2_1_wrapper .uranus .tp-bullet.selected, #rev_slider_2_1_wrapper .uranus .tp-bullet.rs-touchhover{box-shadow:0 0 0 2px rgba(255,255,255,1);border:none;border-radius:50%;background:transparent }#rev_slider_2_1_wrapper .uranus .tp-bullet-inner{-webkit-transition:background-color 0.3s ease,-webkit-transform 0.3s ease;transition:background-color 0.3s ease,transform 0.3s ease;top:0;left:0;width:100%;height:100%;outline:none;border-radius:50%;background-color:rgba(255,255,255,0);background-color:rgba(255,255,255,0.3);text-indent:-999em;cursor:pointer;position:absolute }#rev_slider_2_1_wrapper .uranus .tp-bullet.selected .tp-bullet-inner, #rev_slider_2_1_wrapper .uranus .tp-bullet.rs-touchhover .tp-bullet-inner{transform:scale(0.4);-webkit-transform:scale(0.4);background-color:rgba(255,255,255,1) }.rev-button{position:relative;display:inline-block;padding-left:30px }.rev-button:before{position:absolute;left:5px;top:48%;content:'';width:15px;height:1px;background:#222d35;z-index:9 }.rev-button:hover:before{background:#fff }#rev_slider_3_1_wrapper .hebe.tp-bullets:before{content:' ';position:absolute;width:100%;height:100%;background:transparent;padding:10px;margin-left:-10px;margin-top:-10px;box-sizing:content-box }#rev_slider_3_1_wrapper .hebe .tp-bullet{width:3px;height:3px;position:absolute;background:#ffffff;cursor:pointer;border:5px solid #000000;border-radius:50%;box-sizing:content-box;-webkit-perspective:400;perspective:400;-webkit-transform:translatez(0.01px);transform:translatez(0.01px);transition:all 0.3s }#rev_slider_3_1_wrapper .hebe .tp-bullet.rs-touchhover, #rev_slider_3_1_wrapper .hebe .tp-bullet.selected{background:#000000;border-color:#ffffff }#rev_slider_3_1_wrapper .hebe .tp-bullet-image{position:absolute;width:70px;height:70px;background-position:center center;background-size:cover;visibility:hidden;opacity:0;bottom:3px;transition:all 0.3s;-webkit-transform-style:flat;transform-style:flat;perspective:600;-webkit-perspective:600;transform:scale(0) translateX(-50%) translateY(0%);-webkit-transform:scale(0) translateX(-50%) translateY(0%);transform-origin:0% 100%;-webkit-transform-origin:0% 100%;margin-bottom:15px;border-radius:6px }#rev_slider_3_1_wrapper .hebe .tp-bullet.rs-touchhover .tp-bullet-image{display:block;opacity:1;transform:scale(1) translateX(-50%) translateY(0%);-webkit-transform:scale(1) translateX(-50%) translateY(0%);visibility:visible }#rev_slider_3_1_wrapper .hebe.nav-dir-vertical .tp-bullet-image{bottom:auto;margin-right:15px;margin-bottom:0px;right:3px;transform:scale(0) translateX(0px) translateY(-50%);-webkit-transform:scale(0) translateX(0px) translateY(-50%);transform-origin:100% 0%;-webkit-transform-origin:100% 0% }#rev_slider_3_1_wrapper .hebe.nav-dir-vertical .tp-bullet.rs-touchhover .tp-bullet-image{transform:scale(1) translateX(0px) translateY(-50%);-webkit-transform:scale(1) translateX(0px) translateY(-50%) }#rev_slider_3_1_wrapper .hebe.nav-dir-vertical.nav-pos-hor-left .tp-bullet-image{bottom:auto;margin-left:15px;margin-bottom:0px;left:3px;transform:scale(0) translateX(0px) translateY(-50%);-webkit-transform:scale(0) translateX(0px) translateY(-50%);transform-origin:0% 0%;-webkit-transform-origin:0% 0% }#rev_slider_3_1_wrapper .hebe.nav-dir-vertical.nav-pos-hor-left .tp-bullet.rs-touchhover .tp-bullet-image{transform:scale(1) translateX(0px) translateY(-50%);-webkit-transform:scale(1) translateX(0px) translateY(-50%) }#rev_slider_3_1_wrapper .hebe.nav-pos-ver-top.nav-dir-horizontal .tp-bullet-image{bottom:auto;top:3px;transform:scale(0) translateX(-50%) translateY(0%);-webkit-transform:scale(0) translateX(-50%) translateY(0%);transform-origin:0% 0%;-webkit-transform-origin:0% 0%;margin-top:15px;margin-bottom:0px }#rev_slider_3_1_wrapper .hebe.nav-pos-ver-top.nav-dir-horizontal .tp-bullet.rs-touchhover .tp-bullet-image{transform:scale(1) translateX(-50%) translateY(0%);-webkit-transform:scale(1) translateX(-50%) translateY(0%) }


.pbmit-heading-subheading .pbmit-heading-desc {
    line-height: 1.5;
}

.pbmit-blc-style-1 blockquote {
    font-family: "p_regular";
    font-size: 16px;
}

.pbmit-heading-subheading .pbmit-title {
    font-size: 24px;
}
.pbmit-heading-subheading .pbmit-subtitle {
    font-style: 14px;
}

.pbmit-btn-lg{
    padding: 25px 45px!important;
}
.pbmit-btn-lg:hover{
    color: var(--pbmit-blackish-color) !important;
    background-color: var(--pbmit-white-color) !important;
}
.pbmit-btn {
    display: inline-block;
    text-decoration: none;
    font-family: var(--pbmit-btn-typography-font-family);
    font-size: var(--pbmit-btn-typography-font-size);
    font-weight: var(--pbmit-btn-typography-variant);
    line-height: var(--pbmit-btn-typography-line-height);
    letter-spacing: 1px;
    padding: 18px 38px;
    text-transform:uppercase;
    background-color:var(--pbmit-global-color);
    border: none;
    border-radius: 10px;
    box-shadow : var(--box-shadow-global);
    color: #fff;
    -webkit-transition: all .3s;
    -o-transition: all .3s;
    transition: all .3s;
    cursor: pointer;
    outline: none !important;
    -webkit-font-smoothing: antialiased;
    text-align: center;
    position: relative;
    transition: all .3s ease-in-out;
}
.pbmit-btn span{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}
.pbmit-btn span::before{
    content: '';
    width: 15px;
    height: 1px;
    background: #fff;
    margin-right: 10px;
    margin-top: 8px;
    transition: all .3s ease-in-out;
}
.pbmit-btn:hover, .pbmit-btn:focus {
    color: #ffffff;
    background-color: var(--pbmit-blackish-color);
    box-shadow: var(--box-shadow-blackish);
    outline: none;
}
.pbmit-btn-global{
    background-color: var(--pbmit-global-color);
}
.pbmit-btn-global:hover{
    color: var(--pbmit-global-color);
    background-color: var(--pbmit-white-color);
}
.pbmit-btn-global:hover span::before{
    background: var(--pbmit-global-color);
}
.pbmit-btn-outline{
    padding: 16px 38px;
    background-color: transparent;
    color: var(--pbmit-secondary-color);
    border: 2px solid var(--pbmit-secondary-color);
}
.pbmit-btn-outline span::before{
    transition: all .3s ease-in-out;
    background-color: var(--pbmit-secondary-color);
}
.pbmit-btn-outline:hover span::before{
    background-color: var(--pbmit-white-color);
}
.pbmit-btn-blackish{
    background-color: var(--pbmit-secondary-color);
}
.pbmit-btn-blackish:hover{
    background-color: var(--pbmit-global-color);
}
.pbmit-btn-outline-global{
    background-color: transparent;
    color: var(--pbmit-global-color);
    border: 2px solid var(--pbmit-global-color);
}
.pbmit-btn-outline-global:hover{
    background-color: var(--pbmit-global-color);
}
.pbmit-btn-outline-global span::before{
    background: var(--pbmit-global-color);
}
.pbmit-btn-outline-global:hover span::before{
    background: var(--pbmit-white-color);
}
.pbmit-btn-white{
    color: var(--pbmit-secondary-color);
    background-color: var(--pbmit-white-color);
}
.pbmit-btn-white span::before{
    background: var(--pbmit-secondary-color);
}
.pbmit-btn-white:hover span::before {
    background: var(--pbmit-white-color);
}

.pbmit-ihbox-btn a {
    color:#242424;
}

.pbmit-heading-subheading .pbmit-subtitle {
    font-size: 14px;
}



/** Homepage 03 **/
.counter-three-bg{
    margin-top: -110px;
    z-index: 1;
    position: relative;
}
.about-us-three-img{
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    border:5px solid #ececec;
}
.about-us-three-img .pbmit-ihbox-style-1 {
    border-top-right-radius: 10px;
}

.about-us-three-img .pbmit-ihbox-style-1{
    position: absolute;
    bottom: 0;
    left: 0;
    display: inline-block;
}
.about-us-three-content{
    position: relative;
    padding: 150px 0px 100px 40px;
}
.about-us-three-content::before{
    position: absolute;
    height: 100%;
    width: 200%;
    top: 0;
    left: 0;
    content: "";
    display: block;
    z-index: -1;
    margin: 80px 0px 0px -230px;
    background-color: var(--pbmit-light-color);
}
.about-us-bg{
    background-image: url(../images/homepage-3/img-03.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    padding: 36px 0;
    text-align: center;
    border-radius: 10px;
}
.about-us-three-content .pbmin-lightbox-video{
    width: 58px;
    height: 58px;
    line-height: 58px;
}
.pbmit-blc-style-2 {
    margin-bottom: 50px;
}
.pbmit-blc-style-2 blockquote {
    margin: 0;
    padding: 0;
    font-weight: normal!important;
    position: relative;
    padding-left: 30px;
    font-size: 17px;
    line-height: 26px;
    border-left: 5px solid #c3002f;
}
.testimonial-bg-three{
    padding-top: 100px;
    padding-bottom: 130px;
    background-color: var(--pbmit-secondary-color);
    background-image: url(../images/homepage-3/dot-map-new-02.png);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: auto;
}
.testimonial-content-three.swiper-slider{
    margin-top: -130px;
}


.about-us-three-img img {
    max-width: 100%;
    height: auto;
    min-height: 650px;
    object-fit: cover;

}

.pbmit-blog-style-1 .pbmit-featured-wrapper {
    height:190px;
    overflow: hidden;
    border-radius: 10px;
}
.pbmit-blog-style-1 {
    margin-top:30px;
}

.pbmit-blog-style-1 .pbmit-meta-date-wrapper {
    border-radius: 10px;
}

.pbmit-blog-style-1 .pbminfotech-box-content .pbmit-post-title {
    font-size: 18px;
    line-height: 30px;
    margin-bottom: 15px;
    margin-top:10px;
}

.pbmit-blog-style-1 .pbmit-meta-date-wrapper span {
    font-size: 10px;
}

.pbmit-blog-style-1 .pbminfotech-box-content .pbmit-post-title a {
    color:#242424;
}

.pbmit-read-more-link span {
    font-size:12px;
}

/*.partners-slider .l-item {*/
/*    box-shadow:none;*/
/*}*/

.pbmit-ihbox-style-2 .pbmit-ihbox-box-number {
    background: #fbfbfb;
}

.courses-slider .l-item {
    min-height: 480px;
}

.about-text2 p {
    line-height: 1.5;
    color:#888;
    margin-top:20px;
}

.pbmit-blog-style-1 .post-item {
    border-radius:10px;
}
.about-us-three-content .pbmin-lightbox-video {
    width: 58px;
    height: 58px;
    line-height: 58px;
    background: #fff;
    border-radius: 50%;
    width: 50px!important;
    position: absolute;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    left: calc(50% - 20px);
}

.about-us-bg {

    position: relative;
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.about-us-three-content .pbmin-lightbox-video svg {
    width: 16px;
}

.about-us-three-content .pbmin-lightbox-video svg path{
    stroke: #d02333;
}

.partners  .section-title  ul {
    width: auto;
    display: flex;
    margin-top:0;
    position: absolute;
    right: 15px;
}

.partners  .section-title   ul li a {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border:1px solid #ececec;
    display: flex;
    align-items: center;
    justify-content: center;
    padding:0;
    color:#242424;
    font-size: 16px;
}

.partners  .section-title   ul li a:hover {
    color:#fff;
    border-color:#2f4858;
    background:#2f4858;
}

/** 07 - Contact us **/
.contact-box.pbmit-ihbox-style-7 {
    padding: 50px;
    border: 1px solid #EEEEEE;
}
.contact-form-bg{
    background-image: url(../images/slider-01.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    padding: 100px 0 90px;
}
.contact-form {
    padding: 50px 50px 25px 50px;
    background-color: var(--pbmit-secondary-color);
    background-image: url(../images/dot-map.png);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: auto;
}
.contact-section-iframe {
    filter: brightness( 100% ) contrast( 100% ) saturate( 0% ) blur( 0px ) hue-rotate( 0deg );
}
.contact-section-iframe iframe{
    width: 100%;
    height: 500px;
    margin-bottom: -10px;
}
label.error {
    background: red;
    color: white;
    border-radius: 0px 0 10px 10px;
    margin-bottom: 15px;
    padding: 0 10px;
    font-size: 12px;
    display: block;
    margin-top: -30px;
    margin-right: 20%;
}
.message-status{
    margin-top: 30px;
}
/**08 - Comment Form **/
.form-control{
    background-color:var(--pbmit-white-color);
    color:#848484;
    height: 50px;
    line-height: 50px;
    padding: 20px 10px 20px 20px;
    font-size: 16px;
    font-weight: 400;
    border: 0;
    border-radius: 0px;
    margin-bottom: 30px;
    width: 100%;
}
.form-control::placeholder{
    color:rgb(172, 172, 172);
}
textarea.form-control{
    height: 150px;
}
.form-control:focus{
    color: var(--pbmit-body-typography-color);
}
form button{
    margin-bottom: 30px;
}
.form-style-1 .form-control{
    color: #fff;
    height: 55px;
    background-color: transparent;
    border: 1px solid rgb(255 255 255 / .3);
}
.form-style-1 .form-control::placeholder{
    color:rgb(239, 230, 230);
}
.form-style-1 textarea.form-control {
    height: 120px;
}


.contact-form-bg {
    margin-top:100px;
    background:#242424;
}

.contact-form-bg .bg-img {
    opacity:.3;
}

.pbmit-heading-subheading .pbmit-subtitle {
    font-size: 24px;
    font-family: "p_bold";
}
.pbmit-heading-subheading .pbmit-title {
    font-size: 14px;
}

.pbmit-heading-desc {
    line-height: 1.5;
}

.pbmit-ihbox-style-7 .pbmit-element-title {
    font-size: 18px;
    font-weight: bold;
}

.pbmit-heading-desc ul li a {
    color:#242424;
}

.pbmit-heading-desc ul li a:hover {
    color:#d02333;
}


.faq .collapsible-link {
    white-space:normal;
    padding-right: 30px;
}

@media(max-width:1024px) {

    .show-filter {
        display: flex;
    }
    .courses-filters {
        width: 100%;
        margin-top: 0;
        float: left;
        margin-bottom: 30px;
    }

    .about-header , .about-header__img {
        height: auto;
    }

    .about-header__title {
        height: auto;
        margin-bottom: 40px;
    }

    .footer-block ul {
        margin-bottom:30px;
        width: 100%;
        float:left;
    }
    .partners-item__carousel-item figure img {
        max-height: 30px;
    }
    .c-t {
        width: 100%;
        border:none;
        margin:0;
    }
    .c {
        flex-direction: column;
        align-items: flex-start;
    }
    .cart {
        width: 320px;
    }
    .c-t__infos .country h2 {
        font-size: 12px;
    }
    .c-t__infos .date span {
        font-size: 12px;
    }
    .c-info {

        align-items: flex-start;
        margin-top: 30px;
    }

    .section-title span {
        font-size: 14px;
    }
    .section-title h1 {
        font-size: 21px;
    }

    .courses .section-title ul li {
        font-size: 17px;
    }
    .section-title ul li {
        margin-right: 5px;
    }
    .courses .section-title ul {
        margin-top: 20px;
        right: 15px;
    }
    .spead ul {
        margin-bottom: 30px;
        margin-top: 30px;
    }

    .courses-filters {
        display: none;
    }

    .courses-items {
        width: 100%;
        margin:0;
        padding:0;
    }

    .l-item__img {
        width: 10%;

    }
    .l-item__img .price {
        display: none;
    }
    .price-mob {
        display: flex!important;
        margin-top:10px;
    }

    .why_us-item {
        margin-bottom: 15px;
    }

    .courses-full__infos2  {
        display: none;
    }

    .courses-full .section-title ul {
        margin-top: 20px;
    }

    .courses-full__tabs .tab-content .tab-pane .content-in ul li {
        display: block;
    }

    .courses-full__tabs .tab-content .tab-pane .btns-in {
        flex-direction: column;
    }
    .courses-full__tabs .tab-content .tab-pane .btns-in a {
        width: 100%;
        margin-bottom: 10px;
    }

    .l-c .l-item__img {
        width: 10%;
    }

    .v-title p {
        display:none;
    }
    .video h1 {
        font-size:24px;
    }

    #img {
        order: 1;
    }

    .top-title__title:after {
        display: none;
    }

    .top-title__title {
        border-radius: 20px;
        margin-top: 40px;
        width: 100%;
    }
    .top-title {
        display: flex;
        align-items: center;
        flex-direction: column-reverse;
    }

    .bg-titles {
        max-width: 100%;
    }

    .countdown {
        flex-direction: column;
    }

    .item-c {
        width: 100%;
        margin-bottom: 20px;
    }

    .video-tools {
        min-height: 350px;
    }

    .bg-titles {
        padding:40px 0;
    }
}

.pbmit-heading-subheading .pbmit-subtitle {
    text-transform: none!important;
}

.pbmit-heading-subheading .pbmit-title {
    line-height: 1.5;
}

.pbmit-ihbox-contents {
    display: flex;
    align-items: flex-start;
    flex-direction: column;

}


.pbmit-ihbox-style-6 .pbmit-ihbox-box-number, .pbmit-ihbox-style-6 .pbmit-element-title {
    font-family: "p_regular";
}


.pbmit-heading-subheading .pbmit-title {
    margin-top:10px;
}

.pbmit-element-title {
    font-size: 18px!important;
    padding-left:0px;
}

.footer .footer-block ul li  svg {
    display: none;
}

.menu-popup ul li a svg path {
    fill: #fff;
}

.menu-popup ul li a svg  {
fill:#fff
}

.ctaa .pbmit-ihbox-contents {
flex-direction: column;
    align-items: flex-start;
}

.ctaa .pbmit-element-title {
    padding-left: 0;
}

.pbmit-service-style-3 .pbmit-service-title {
    font-size:14px;
    line-height: 1.5;
    margin-top:10px;
}

.pbmit-service-style-3 .pbmit-service-title a {
    color:#242424;
}

.pbmit-service-style-3 .pbmit-featured-wrapper , .pbmit-service-style-3 .pbmit-featured-wrapper img , .pbmit-service-style-3 .pbminfotech-box-content {
    border-radius: 10px;
}

.blog-content ul li, .blog-content ol li {
    font-size: 14px;
}

.pbmit-service-style-3 .pbmit-featured-wrapper {
    height: 300px;
}

.single-blog-post .blog-content p {
    margin-top:0;
}

.pbmit-port-cat, .pbmit-service-cat {
    font-family: "p_regular";
    letter-spacing: 0;
    line-height: 1.5;
}

.pbmit-service-style-3 .pbmit-service-title a {
    color:#888;
}

.services-item__title p {
    color:#888;
}

.services-item__title h1, services-item__title h2, .services-item__title h3, .services-item__title h4 {
    color:#242424;
}

.services-item__title h2 {
    color:#242424;
    font-family: "p_regular";
    font-weight: normal;
}


.courses-full__tabs .tab-content .tab-pane .content-in p strong {
    font-family: "p_regular";
    font-weight: 600;
}

.courses-full__tabs .tab-content .tab-pane .content-in p em {
    font-style: italic;
}


.courses-full__tabs .tab-content .tab-pane .content-in  blockquote {
    border-left:5px solid #d02333;
    padding-left:15px;
    float:left;
    width: calc(100% - 15px);
    margin-left:15px;
    margin-bottom: 15px;
}

.courses-full__tabs .tab-content .tab-pane .content-in  blockquote p {
    margin-bottom: 0;
}



@media only screen and (min-width: 1400px) and (max-width: 1660px) {

    .video , .v-video {
        height: calc(100vh - 280px);
    }
}


@media (max-width:1024px) {
    .v-title p {
        display: block;
    }

}


#slider .courses-slider__item {
    min-height: 86px;
}

.all-courses__btn:hover {
    color:#fff;
}

.faq .collapsible-link {
    border-bottom:1px solid #dedede;
    color:#242424;
    font-weight: bold;
    font-size: 14px;
    text-decoration: none!important;
}

.faq .collapsible-link:hover {

    text-decoration: none;
}


.section-xxl .pbmit-service-style-3 {
    margin-bottom: 45px;
}

.pbmit-ihbox-style-2.pbmit-ihbox h2 {
    font-weight: bold;
}

.pbmit-heading-desc {
    margin-bottom: 50px;
}

.ctaa {
    padding-top:30px;
}


.text strong {
    font-weight: bold;
    padding-right: 5px;
}

.text em {
    font-style: italic;
}

.text u {
    text-decoration: underline;
}

.text p {
    margin-bottom: 20px;
    line-height: 1.7;
    color: #242424
}

.text h3 {
    margin-bottom: 15px;
    color: #242424;
    font-family: "p_regular";
    font-weight: bold;
}

.text ul ,
.text ol {
    padding-left: 15px;
    width: 100%;
    float:left;
    margin-bottom: 15px;
    counter-reset: section;
}

.text ul li , .text ol li {
    margin-bottom: 5px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: baseline;
    -ms-flex-align: baseline;
    align-items: baseline;
    line-height: 1.7;

}

.text ul li:before {
    content: "\eaa0";
    width: 20px;
    font-family: Icofont;
    height: 20px;
    float: left;
    margin-right: 15px;
    background: #2f4858;
    border-radius: 50%;
    color: #fff;
    line-height: 21px;
    padding-left: 4px;
}

.text ol li:before {
    counter-increment: section;
    content: "" counter(section) "";
    width: 20px;
    height: 20px;
    float: left;
    margin-right: 15px;
    background: #2f4858;
    border-radius: 50%;
    color: #fff;
    line-height: 21px;
    text-align: center;
    font-size:9pt;
}

.text ul li:last-child {
    margin-bottom: 0
}

.pt-30px {
    padding-top: 30px;
}

.mt-80 {
    margin-top:80px;
}

section.text {
    padding-top:20px;
}

.about-one-img , .pbmit-blc-style-1 {
    padding-top:0;
}

.no-c:before {
    background:none;
    background-image: none;
}

.no-c .section-title h1 {
    color:#d02333!important;
}
.no-c .section-title span {
    color:#242424!important;;
}

.no-c .section-title ul li {
    color:#242424!important;;
}
.title-2 svg {
    width: 200px;
    height: 80px;
    margin-left: -60px;
    float: left;
    margin-right: 30px;
}
.title-2 svg path {
    fill: #d02333;
}

.title-2 {
    display: flex;
    align-items: center;
}

.main-search {
    width: 100%;
    float: left;
    padding: 10px 0;
    border-bottom: 1px solid #dedede;
    display: none;
}

.main-search form {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.main-search form  input {
    padding: 13px 30px;
    background: #f2f2f2;
    border-radius: 10px;
    color: #888;
    width: calc(100% - 215px);
}

.main-search form button {
    background: #d02333;
    padding: 15px 30px;
    border-radius: 10px;
    color:#fff;
    cursor: pointer;
    margin-bottom: 0;
}

.custom-checkbox {
    display: flex;
    align-items: center;
    margin-right: 30px;
}

.checkboxs , .radios {
    display: flex;
    align-items: center;
    margin-top: 20px;
}

.radios {
    margin-bottom: 20px;
}

.custom-checkbox .custom-control-input:indeterminate~.custom-control-label::before {
    background: #d02333;
}

.f-c {
    width: 100%;
    float: left;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.f-c img {
    width: 100px;
    height: auto;
    object-fit: contain;

}

.f-c h2 {
    margin:15px 0;
    width: 100%;
    display: inline-block;
    text-align: center;
}

.f-t {
    width: 100%;
    float: left;
    text-align: center;
}

.form {
    padding-top: 80px;
}

.form form button {
    padding: 15px 30px;
    border-radius: 10px;
    color: #fff;
    background: #d02333;
    float: left;
    margin-top: 30px;
    margin-left: 15px;
    cursor: pointer;
}